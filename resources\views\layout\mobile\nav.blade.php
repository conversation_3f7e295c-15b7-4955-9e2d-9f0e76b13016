{{--
    plan_id: 1,'AX-MT-STR-M','Starter'
    plan_id: 2,'AX-MT-PRO-M','Professional'
    plan_id: 3,'AX-MT-ENT-M','Enterprise'
    plan_id: 4,'AX-MT-STR-A','Starter'
    plan_id: 5,'AX-MT-PRO-A','Professional'
    plan_id: 6,'AX-MT-ENT-A','Enterprise'
    plan_id: 7,'AX-MT-FREE-M','Free'
--}}
<!-- main menu-->
<div data-scroll-to-active="false" class="main-menu menu-fixed menu-dark menu-accordion menu-shadow">
    <!-- main menu header-->
    <!-- / main menu header-->
    <!-- main menu content-->
    @php
        @$plan = \App\SiteSetting::select('plan_id')
            ->where('site_id', @auth()->user()->site_id)
            ->where('status', 1)
            ->value('plan_id');
        $plan = @customDecrypt($plan);

        $starterOrFree = $starterOnly = false;

        if ($plan == 1 || $plan == 4) {
            $starterOnly = true;
        }
        if ($plan == 1 || $plan == 4 || $plan == 7) {
            $starterOrFree = true;
        }
    @endphp

    <div class="main-menu-content">
        <ul id="main-menu-navigation" data-menu="menu-navigation" class="navigation navigation-main">
            <li class=" nav-item"><a href="{{ route('mobile') }}"><i class="icon-home3"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.title.home') }}</span></a>
            </li>
            <li class=" nav-item {{ request()->segment(2) == 'inquiry' ? 'open' : '' }}"><a href="#"><i
                        class="icon-search7"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.inquiry') }}</span></a>
                <ul class="menu-content">
                    <li>
                        <a href="{{ route('ItemInquiry') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.item_inquiry') }} </a>
                    </li>
                    @if (config('icapt.special_modules.enable_pallet') && !$starterOrFree)
                        <li>
                            <a href="{{ route('PalletInquiry') }}" data-i18n="nav.dash.ecommerce"
                                class="menu-item">{{ __('mobile.nav.pallet_inquiry') }}</a>
                        </li>
                    @endif
                    <li>
                        <a href="{{ route('locinquiry') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.loc_inquiry') }}</a>
                    </li>
                    @if ($plan != 1 && $plan != 4)
                        <li>
                            <a href="{{ route('Job_Inquiry') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.job_inquiry') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('JobInquiry') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.job_att_inquiry') }}</a>
                        </li>
                    @endif
                    {{-- <li><a href="{{route('poReceive')}}" data-i18n="nav.dash.analytics" class="menu-item">Warehouse Inquiry</a>
                        </li> --}}
                    {{--  <li><a href="{{route('ContainerInquiry')}}" data-i18n="nav.dash.analytics" class="menu-item">Container Inquiry</a>
                        </li>
                        <li><a href="{{route('ItemContainerInquiry')}}" data-i18n="nav.dash.analytics" class="menu-item">Item Container Inquiry</a>
                        </li>  --}}
                </ul>
            </li>
            <li class=" nav-item {{ request()->segment(2) == 'warehouse' ? 'open' : '' }}"><a href="#"><i
                        class="icon-industry"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.warehouse') }}</span></a>
                <ul class="menu-content">
                    <li>
                        <a href="{{ route('StockMove') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.stock_move') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('multiStockMove') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('admin.label.multi') }} {{ __('mobile.nav.stock_move') }}</a>
                    </li>
                    {{--  <li><a href="{{route('putaway')}}" data-i18n="nav.dash.ecommerce" class="menu-item">Putaway</a>
                        </li>  --}}
                    <li>
                        <a href="{{ route('MiscIssue') }}" data-i18n="nav.dash.analytics"
                            class="menu-item">{{ __('mobile.nav.misc_issue') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('MiscReceipt') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.misc_receipt') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('TOLoss') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.to_loss') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('counters') }}" data-i18n="nav.dash.counts"
                            class="menu-item">{{ __('mobile.nav.inv_count') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('countersBatch') }}" data-i18n="nav.dash.counts"
                            class="menu-item">{{ __('mobile.label.batch') }} {{ __('mobile.nav.inv_count') }}</a>
                    </li>
                    {{-- <li>
                            <a href="{{route('picks')}}" data-i18n="nav.dash.counts" class="menu-item">{{__('mobile.nav.picklist')}}</a>
                        </li> --}}

                    <li>
                        <a href="{{ route('picksTest') }}" data-i18n="nav.dash.counts" class="menu-item">Pick List</a>
                    </li>

                </ul>
            </li>
            <li class=" nav-item {{ request()->segment(2) == 'incoming' ? 'open' : '' }}"><a href="#"><i
                        class="icon-inbox2"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.incoming') }}</span></a>
                <ul class="menu-content">
                    <li>
                        <a href="{{ route('poReceive') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.po_receipt') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('putaway') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.put_away') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('CoReturn') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.co_return') }}</a>
                    </li>


                    @if (config('icapt.enable_customer_returns'))
                        <li>
                            <a href="{{ route('CustomerReturn') }}" data-i18n="nav.dash.ecommerce"
                                class="menu-item">{{ __('mobile.nav.customer_return') }}</a>
                        </li>
                    @endif



                    @if (@$plan != 7 && @$plan != 1 && @$plan != 4)
                        <li>
                            <a href="{{ route('TOReceipt') }}" data-i18n="nav.dash.analytics"
                                class="menu-item">{{ __('mobile.nav.to_receipt') }}</a>
                        </li>
                    @endif
                </ul>
            </li>
            <li class=" nav-item {{ request()->segment(2) == 'outgoing' ? 'open' : '' }}"><a href="#"><i
                        class="icon-outbox"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.outgoing') }}</span></a>
                <ul class="menu-content">
                    <li>
                        <a href="{{ route('poReturn') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.po_return') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('PickNShip') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.picknship') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('CoPick') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.co_picking') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('CoUnPick') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.co_unpick') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('CoShipping') }}" data-i18n="nav.dash.analytics"
                            class="menu-item">{{ __('mobile.nav.co_shipping') }}</a>
                    </li>
                    @if (@$plan != 7 && @$plan != 1 && @$plan != 4)
                        <li>
                            <a href="{{ route('TransferOrderShipping') }}" data-i18n="nav.dash.analytics"
                                class="menu-item">{{ __('mobile.nav.to_shipping') }}</a>
                        </li>
                    @endif
                </ul>
            </li>

            @if (@$plan != 1 && @$plan != 4)
                <li class=" nav-item {{ request()->segment(2) == 'wip' ? 'open' : '' }}"><a href="#"><i
                            class="icon-cube"></i><span data-i18n="nav.dash.main"
                            class="menu-title">{{ __('WIP') }} </span></a>
                    <ul class="menu-content">
                        <li>
                            <a href="{{ route('JobMaterialIssue') }}" data-i18n="nav.dash.ecommerce"
                                class="menu-item">{{ __('mobile.nav.job_matl_issue') }}</a>
                        </li>
                        @if (Gate::allows('hasBatchJobMatlIssue'))
                            <li>
                                <a href="{{ route('BatchJobMaterialIssue') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('mobile.label.batch') }}
                                    {{ __('mobile.nav.job_matl_issue') }}</a>
                            </li>
                        @endif

                        <!-- <li><a href="{{ route('JobMaterialIssueByJob') }}" data-i18n="nav.dash.ecommerce" class="menu-item">Job Material Issue By Job</a>
                            </li> -->
                        <li>
                            <a href="{{ route('unIssueIndex') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.job_matl_return') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('JobReceipt') }}" data-i18n="nav.dash.analytics"
                                class="menu-item">{{ __('mobile.nav.job_receipt') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('JobReturn') }}" data-i18n="nav.dash.analytics"
                                class="menu-item">{{ __('mobile.nav.job_return') }}</a>
                        </li>
                        @if (App\View\TparmView::multipleJob('JobLabour', 'allow_multiple_job', @auth()->user()->site_id) == 1)
                            <li>
                                <a href="{{ route('LabourReporting') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('mobile.nav.start_labor') }}</a>
                            </li>
                            <li>
                                <a href="{{ route('EndLabourReporting') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('mobile.nav.end_labor') }}</a>
                            </li>
                        @else
                            <li>
                                <a href="{{ route('LabourReporting') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('mobile.nav.labor_reporting') }}</a>
                            </li>
                        @endif

                        @if (@$plan == 3 || @$plan == 6)
                            @if (App\View\TparmView::multipleJob('MachineRun', 'allow_multiple_job', @auth()->user()->site_id) == 1)
                                <li>
                                    <a href="{{ route('MachineRunStart') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('mobile.nav.start_machine_run') }}</a>
                                </li>
                                <li>
                                    <a href="{{ route('MachineRunEnd') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('mobile.nav.end_machine_run') }}</a>
                                </li>
                                <li>
                                    <a href="{{ route('MachineDownTimeStart') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('mobile.nav.start_machine_downtime') }}</a>
                                </li>
                                <li>
                                    <a href="{{ route('MachineDownTimeEnd') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('mobile.nav.end_machine_downtime') }}</a>
                                </li>
                            @else
                                <li>
                                    <a href="{{ route('MachineRun') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('mobile.nav.machine_run') }}</a>
                                </li>
                            @endif
                        @endif


                        <li>
                            <a href="{{ route('WIPMove') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.wip_move') }}</a>
                        </li>
                        @if (config('icapt.client_prefix') == 'OceanCash')
                            <li>
                                <a href="{{ route('BundleMobile') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('mobile.nav.bundle_builder') }}</a>
                            </li>
                        @endif
                    </ul>
                </li>
            @endif
            @if (config('icapt.special_modules.enable_pallet') && !$starterOrFree)
                <li class=" nav-item {{ request()->segment(2) == 'pallet' ? 'open' : '' }}"><a href="#"><i
                            class="icon-industry"></i><span data-i18n="nav.dash.main"
                            class="menu-title">{{ __('mobile.nav.pallet') }}</span></a>
                    <ul class="menu-content">
                        <li>
                            <a href="{{ route('PalletMove') }}" data-i18n="nav.dash.ecommerce"
                                class="menu-item">{{ __('mobile.nav.pallet_move') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('PalletLetdown') }}" data-i18n="nav.dash.analytics"
                                class="menu-item">{{ __('mobile.nav.pallet_letdown') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('PalletItemTransfer') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.pallet_transfer') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('PalletBuilder') }}" data-i18n="nav.dash.counts"
                                class="menu-item">{{ __('mobile.nav.pallet_builder') }}</a>
                        </li>

                        <li>
                            <a href="{{ route('PalletDestruction') }}" data-i18n="nav.dash.counts"
                                class="menu-item">{{ __('mobile.nav.pallet_destruction') }}</a>
                        </li>
                    </ul>
                </li>
            @endif
            {{-- Print Label --}}
            <li class=" nav-item {{ request()->segment(2) == 'print-label' ? 'open' : '' }}"><a href="#"><i
                        class="icon-paper"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.print_label') }}</span></a>
                <ul class="menu-content">
                    <li>
                        <a href="{{ route('showInventoryLabel') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.inv_label') }} </a>
                    </li>
                    <li>
                        <a href="{{ route('showCOShippingLabel') }}" data-i18n="nav.dash.ecommerce"
                            class="menu-item">{{ __('mobile.nav.co_label') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('showPORcptLabel') }}" data-i18n="nav.dash.project"
                            class="menu-item">{{ __('mobile.nav.po_label') }} </a>
                    </li>
                    @if (!$starterOnly)
                        <li>
                            <a href="{{ route('showJobMatLabel') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.job_matl_label') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('showJobRcptLabel') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.job_receipt_label') }}</a>
                        </li>
                    @endif
                    @if (!$starterOrFree)
                        <li>
                            <a href="{{ route('showTOShippingLabel') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.to_shipping_label') }}</a>
                        </li>
                        <li>
                            <a href="{{ route('showPalletLabel') }}" data-i18n="nav.dash.project"
                                class="menu-item">{{ __('mobile.nav.pallet_label') }}</a>
                        </li>
                    @endif
                </ul>

            </li>

            @if (@auth()->user()->homepage == 'web' || @auth()->user()->homepage == 'mobile')
                <li class=" nav-item">
                    <a href="{{ route('web') }}"><i class="icon-cog3 spinner"></i><span data-i18n="nav.dash.main"
                            class="menu-title">{{ __('mobile.nav.switch_to_web') }}</span></a>
                </li>
            @elseif(@auth()->user()->site_administration == 'Yes')
                <li class=" nav-item">
                    <a href="{{ route('webMenuFromMobile') }}"><i class="icon-cog3 spinner"></i><span
                            data-i18n="nav.dash.main"
                            class="menu-title">{{ __('mobile.nav.switch_to_web') }}</span></a>
                </li>
            @endif
            <li class=" nav-item">
                <a href="{{ route('logout') }}"><i class="icon-power3"></i><span data-i18n="nav.dash.main"
                        class="menu-title">{{ __('mobile.nav.logout') }}</span></a>
            </li>
        </ul>
    </div>
</div>
<!-- / main menu-->
