@extends('layout.mobile.app')
@section('content')
@section('title', __('mobile.title.to_loss'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block pr-1">
        <form class="form" autocomplete="off" id="TOLossform" name="TOLossform"
            action="{{ route('TOLossItemList') }}" method="get">
            @csrf
            <div class="form-body">
                <div id="scandiv">
                    @include('components.form.scan_input', ['type' => 'inventory'])
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="whse_num">{{ __('mobile.label.warehouse') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <select class="form-control" id="whse_num" name="whse_num" required>
                            <option value="">{{ __('mobile.label.select_warehouse') }}</option>
                            @foreach ($warehouses as $warehouse)
                                <option value="{{ $warehouse->whse_num }}" 
                                    {{ old('whse_num', request('whse_num')) == $warehouse->whse_num ? 'selected' : '' }}>
                                    {{ $warehouse->whse_num }} - {{ $warehouse->whse_desc }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <button type="button" id="whse_num_btn"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#whseModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="trn_num">{{ __('mobile.label.transfer_order') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <input type="text" id="trn_num" class="form-control" placeholder="{{ __('mobile.placeholder.transfer_order') }}"
                            name="trn_num" value="{{ old('trn_num', request('trn_num')) }}" required>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <button type="button" id="trn_num_btn"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#trnModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="item_num">{{ __('mobile.label.item') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <input type="text" id="item_num" class="form-control" placeholder="{{ __('mobile.placeholder.item_optional') }}"
                            name="item_num" value="{{ old('item_num', request('item_num')) }}">
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <button type="button" id="item_num_btn"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#itemModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-actions center">
                    <button type="submit" id="search" class="btn btn-primary submitloader">
                        <i class="icon-search"></i> {{ __('mobile.button.search') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Warehouse Selection Modal -->
<div class="modal fade text-xs-left" id="whseModal" tabindex="-1" role="dialog" aria-labelledby="whseModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title modalheader"><b>{{ __('mobile.title.select_warehouse') }}</b></h4>
            </div>
            <div class="modal-body">
                <div class="container">
                    @foreach ($warehouses as $warehouse)
                        <div class="row" style="cursor: pointer;" onclick="selectWarehouse('{{ $warehouse->whse_num }}', '{{ $warehouse->whse_desc }}')">
                            <div class="col-xs-12">
                                <strong>{{ $warehouse->whse_num }}</strong> - {{ $warehouse->whse_desc }}
                            </div>
                        </div>
                        <hr>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function selectWarehouse(whseNum, whseDesc) {
        $('#whse_num').val(whseNum);
        $('#whseModal').modal('hide');
    }

    $(document).ready(function() {
        // Auto-focus on transfer order field when warehouse is selected
        $('#whse_num').change(function() {
            if ($(this).val()) {
                $('#trn_num').focus();
            }
        });

        // Auto-submit form when transfer order is scanned/entered
        $('#trn_num').on('change blur', function() {
            if ($(this).val() && $('#whse_num').val()) {
                // Small delay to allow for barcode scanning completion
                setTimeout(function() {
                    $('#TOLossform').submit();
                }, 500);
            }
        });

        // Handle scan input
        $(document).on('change', '#scan_input', function() {
            var scannedValue = $(this).val();
            if (scannedValue) {
                // Try to parse as transfer order first
                if (!$('#trn_num').val()) {
                    $('#trn_num').val(scannedValue);
                    $('#trn_num').trigger('change');
                } else if (!$('#item_num').val()) {
                    $('#item_num').val(scannedValue);
                }
                $(this).val(''); // Clear scan input
            }
        });
    });
</script>

@include('util.selection')
@endsection
