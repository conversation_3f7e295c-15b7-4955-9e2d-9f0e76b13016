@extends('layout.mobile.app')
@section('content')
@section('title', __('mobile.title.to_loss_items'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }
    
    .item-row {
        cursor: pointer;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }
    
    .item-row:hover {
        background-color: #f5f5f5;
    }
    
    .item-info {
        font-size: 14px;
    }
    
    .item-qty {
        font-weight: bold;
        color: #007bff;
    }
    
    .catch-weight-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block">
        <div class="row mb-2">
            <div class="col-xs-12">
                <h5><strong>{{ __('mobile.label.transfer_order') }}:</strong> {{ $trn_num }}</h5>
                <h6><strong>{{ __('mobile.label.warehouse') }}:</strong> {{ $whse_num }}</h6>
            </div>
        </div>

        @if($data->count() > 0)
            <div class="row">
                <div class="col-xs-12">
                    <h6><strong>{{ __('mobile.label.items_available_for_loss') }}:</strong></h6>
                </div>
            </div>

            @foreach($data as $item)
                @php
                    $qtyAvailableForLoss = $item->qty_shipped - $item->qty_received - ($item->qty_loss ?? 0);
                @endphp
                <div class="item-row" onclick="selectItem('{{ $item->trn_line }}', '{{ $item->item_num }}')">
                    <div class="row">
                        <div class="col-xs-8">
                            <div class="item-info">
                                <strong>{{ $item->trn_line }} - {{ $item->item_num }}</strong>
                                @if($item->item && $item->item->catch_weight == 1)
                                    <span class="catch-weight-badge">{{ __('mobile.label.catch_weight') }}</span>
                                @endif
                            </div>
                            <div class="item-info text-muted">
                                {{ $item->item->item_desc ?? '' }}
                            </div>
                            <div class="item-info">
                                <small>
                                    {{ __('mobile.label.shipped') }}: {{ numberFormatPrecision($item->qty_shipped, 2) }} {{ $item->uom }}<br>
                                    {{ __('mobile.label.received') }}: {{ numberFormatPrecision($item->qty_received, 2) }} {{ $item->uom }}<br>
                                    {{ __('mobile.label.lost') }}: {{ numberFormatPrecision($item->qty_loss ?? 0, 2) }} {{ $item->uom }}
                                </small>
                            </div>
                        </div>
                        <div class="col-xs-4 text-right">
                            <div class="item-qty">
                                {{ numberFormatPrecision($qtyAvailableForLoss, 2) }}
                            </div>
                            <div class="text-muted">
                                <small>{{ $item->uom }}</small>
                            </div>
                            <div class="text-muted">
                                <small>{{ __('mobile.label.available_for_loss') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <div class="row">
                <div class="col-xs-12 text-center">
                    <div class="alert alert-info">
                        <i class="icon-info"></i>
                        {{ __('mobile.message.no_items_available_for_loss') }}
                    </div>
                </div>
            </div>
        @endif

        <div class="form-actions center mt-3">
            <button type="button" class="btn btn-warning mr-1" onclick="goBack()">
                <i class="icon-arrow-left"></i> {{ __('mobile.button.back') }}
            </button>
        </div>
    </div>
</div>

<script>
    function selectItem(trnLine, itemNum) {
        var url = "{{ route('TOLossProcess') }}";
        var params = {
            whse_num: "{{ $whse_num }}",
            trn_num: "{{ $trn_num }}",
            trn_line: trnLine,
            item_num: itemNum
        };
        
        // Build query string
        var queryString = Object.keys(params).map(key => key + '=' + encodeURIComponent(params[key])).join('&');
        window.location.href = url + '?' + queryString;
    }

    function goBack() {
        window.location.href = "{{ route('TOLoss') }}";
    }

    $(document).ready(function() {
        // Handle scan input for quick item selection
        $(document).on('change', '#scan_input', function() {
            var scannedValue = $(this).val();
            if (scannedValue) {
                // Try to find matching item
                var matchedItem = null;
                @foreach($data as $item)
                    if ("{{ $item->item_num }}" === scannedValue || "{{ $item->trn_line }}" === scannedValue) {
                        matchedItem = {
                            trn_line: "{{ $item->trn_line }}",
                            item_num: "{{ $item->item_num }}"
                        };
                    }
                @endforeach
                
                if (matchedItem) {
                    selectItem(matchedItem.trn_line, matchedItem.item_num);
                } else {
                    alert("{{ __('mobile.message.item_not_found') }}");
                }
                $(this).val(''); // Clear scan input
            }
        });
    });
</script>

@endsection
