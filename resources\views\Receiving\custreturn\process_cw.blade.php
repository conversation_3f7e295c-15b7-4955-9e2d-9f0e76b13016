@extends('layout.mobile.app')
@section('content')
@section('title', __('Customer Return - Catch Weight'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$Cust_Return->whse_num"
            :itemnum="$Cust_Return->item_num"
            :itemdesc="$Cust_Return->item_desc"
            :refnum="$Cust_Return->return_num"
            :refline="$Cust_Return->return_line"
            :qtybalance="$Cust_Return->qty_required ?? 0"
            :qtybalanceuom="$Cust_Return->uom"
            :submiturl="route('runCustomerReturnCWProcess')"
            :catch-weight-tolerance="$Cust_Return->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_return"
            :line-uom="$Cust_Return->uom"
            :print-label="$printLabel"
            transtype="cr"
            trans-type="CustomerReturn"
            :incoming="true">

            <!-- Additional Customer Return specific hidden fields -->
            <input type="hidden" name="return_num" value="{{ $Cust_Return->return_num }}">
            <input type="hidden" name="return_line" value="{{ $Cust_Return->return_line }}">
            <input type="hidden" name="cust_num" value="{{ $cust_num }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num"  maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.return_num') }}: {{ $Cust_Return->return_num }} | {{ __('mobile.label.return_line') }}: {{ $Cust_Return->return_line }}
                        </p>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.cust_num') }}: {{ $cust_name }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

@endsection
