<?php

namespace App\Http\Controllers\Warehouse;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use App\TransferLine;
use App\TransferOrder;
use App\Item;
use App\Warehouse;
use App\Loc;
use App\ReasonCode;
use App\Services\CatchWeightService;
use App\Services\GeneralService;
use App\Services\UOMService;
use App\Services\LotService;
use App\View\TparmView;
use App\SiteSetting;
use Timezone;
use DB;
use Alert;

class TOLossController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:hasTOLoss');
    }

    /**
     * Display TO Loss index page
     */
    public function index()
    {
        $warehouses = Warehouse::where('whse_status', 1)->get();
        return view('warehouse.toloss.index')->with('warehouses', $warehouses);
    }

    /**
     * Get TO Loss item list
     */
    public function getToLossItemList(Request $request)
    {
        $validated = $request->validate([
            'whse_num' => 'required|exists:warehouses',
            'trn_num' => 'required'
        ]);

        $whse_num = $request->whse_num;
        $trn_num = $request->trn_num;
        $query = $request->query ?? '';
        $item_num = $request->item_num ?? '';

        // Check if TO exists and is valid for loss processing
        $transferOrder = TransferOrder::where('trn_num', $trn_num)->first();
        if (!$transferOrder) {
            throw ValidationException::withMessages(['trn_num' => 'Transfer Order not found']);
        }

        // Get transfer lines that have shipped quantity but can still have losses
        $transferLines = TransferLine::with('item')
            ->where('trn_num', $trn_num)
            ->where('to_whse', $whse_num)
            ->where('item_num', 'like', '%' . $item_num . '%')
            ->whereRaw('IFNULL(qty_shipped,0) > IFNULL(qty_received,0) + IFNULL(qty_loss,0)')
            ->where('line_stat', '!=', 'C')
            ->whereHas('item', function ($q) {
                return $q->where('item_status', 1);
            });

        if ($query != '') {
            $transferLines = $transferLines->where(function ($q) use ($query) {
                $q->orwhere('trn_line', 'like', '%' . $query . '%')
                    ->orWhere('item_num', 'like', '%' . $query . '%');
            });
        }

        $data = $transferLines->orderByRaw('cast(trn_line as unsigned) ASC')->get();

        return view('warehouse.toloss.item_list')
            ->with('data', $data)
            ->with('whse_num', $whse_num)
            ->with('trn_num', $trn_num);
    }

    /**
     * Show TO Loss process form
     */
    public function process(Request $request)
    {
        $validated = $request->validate([
            'whse_num' => 'required|exists:warehouses',
            'trn_num' => 'required',
            'trn_line' => 'required',
            'item_num' => 'required|exists:items'
        ]);

        $whse_num = $request->whse_num;
        $trn_num = $request->trn_num;
        $trn_line = $request->trn_line;
        $item_num = $request->item_num;

        // Get transfer line details
        $transferLine = TransferLine::with('item')
            ->where('trn_num', $trn_num)
            ->where('trn_line', $trn_line)
            ->where('item_num', $item_num)
            ->first();

        if (!$transferLine) {
            throw ValidationException::withMessages(['details' => 'Transfer Order Line not found']);
        }

        // Check if item is catch weight enabled
        $item = Item::select('lot_tracked', 'catch_weight', 'catch_weight_tolerance')
            ->where('item_num', $item_num)
            ->first();

        // Calculate available quantity for loss
        $qtyAvailableForLoss = $transferLine->qty_shipped - $transferLine->qty_received - $transferLine->qty_loss;

        if ($qtyAvailableForLoss <= 0) {
            throw ValidationException::withMessages(['details' => 'No quantity available for loss processing']);
        }

        // Get reason codes for TO Loss
        $reasonCodes = ReasonCode::where('reason_class', 'TOLoss')
            ->where('reason_status', 1)
            ->get();

        // Get transit location
        $transferOrder = TransferOrder::where('trn_num', $trn_num)->first();
        $transitLoc = $transferOrder->trn_loc;

        // Check if catch weight item
        if ($item && $item->catch_weight == 1) {
            // Render catch weight form
            return view('warehouse.toloss.process_cw')
                ->with('transferLine', $transferLine)
                ->with('item', $item)
                ->with('qtyAvailableForLoss', $qtyAvailableForLoss)
                ->with('reasonCodes', $reasonCodes)
                ->with('transitLoc', $transitLoc)
                ->with('whse_num', $whse_num);
        } else {
            // Render standard form
            return view('warehouse.toloss.process')
                ->with('transferLine', $transferLine)
                ->with('item', $item)
                ->with('qtyAvailableForLoss', $qtyAvailableForLoss)
                ->with('reasonCodes', $reasonCodes)
                ->with('transitLoc', $transitLoc)
                ->with('whse_num', $whse_num);
        }
    }

    /**
     * Process standard TO Loss
     */
    public function processToLoss(Request $request)
    {
        $validated = $request->validate([
            'whse_num' => 'required|exists:warehouses',
            'trn_num' => 'required',
            'trn_line' => 'required',
            'item_num' => 'required|exists:items',
            'qty_loss' => 'required|numeric|min:0.01',
            'reason_code' => 'required|exists:reason_codes,reason_num'
        ]);

        DB::beginTransaction();
        try {
            $this->processToLossTransaction($request);
            DB::commit();

            Alert::success(__('mobile.message.to_loss_success'));
            return redirect()->route('TOLossItemList', [
                'whse_num' => $request->whse_num,
                'trn_num' => $request->trn_num
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Process catch weight TO Loss
     */
    public function processToLossCW(Request $request)
    {
        // Get transfer line details for tolerance validation
        $transferLine = TransferLine::with('item')
            ->where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->where('item_num', $request->item_num)
            ->first();

        if (!$transferLine) {
            throw ValidationException::withMessages(['details' => 'Transfer Order Line not found']);
        }

        // Calculate available quantity for loss as tolerance
        $tolerance = $transferLine->qty_shipped - $transferLine->qty_received - $transferLine->qty_loss;
        $tolerance_uom = $transferLine->uom;

        DB::beginTransaction();
        try {
            // Process catch weight TO Loss using CatchWeightService
            CatchWeightService::processToLossCatchWeight($request, $tolerance, $tolerance_uom);

            // Calculate total loss quantity for success message
            $totalQtyLoss = 0;
            foreach ($request->arr_qty as $qty) {
                $totalQtyLoss += $qty;
            }

            DB::commit();

            Alert::success(__('mobile.message.to_loss_cw_success', ['qty' => $totalQtyLoss]));
            return redirect()->route('TOLossItemList', [
                'whse_num' => $request->whse_num,
                'trn_num' => $request->trn_num
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Common method to process TO Loss transaction
     */
    private function processToLossTransaction($request)
    {
        // Convert UOM for loss quantity
        $toLossRequest = clone $request;
        $toLossRequest = UOMService::convertTORequestForQtyLost($toLossRequest);

        // Create TO Loss material transaction
        $TOLossMatlTrans = GeneralService::newTOLossMatlTrans(
            config('icapt.transtype.to_lost'),
            $toLossRequest
        );

        // Update transfer line qty_loss
        $transferLine = TransferLine::where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->where('item_num', $request->item_num)
            ->first();

        if ($transferLine) {
            $transferLine->qty_loss = ($transferLine->qty_loss ?? 0) + $request->qty_loss;
            $transferLine->save();
        }

        // Update lot if lot tracked
        if ($request->lot_num) {
            LotService::updateLot("TO Loss", $request);
        }

        return $TOLossMatlTrans;
    }
}
