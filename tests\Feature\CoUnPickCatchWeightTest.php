<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\User;
use App\Warehouse;
use App\Loc;
use App\Item;
use App\ItemLoc;
use App\LotLoc;
use App\CustomerOrderItem;
use App\StageLoc;
use App\CustomerOrder;

class CoUnPickCatchWeightTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $warehouse;
    protected $location;
    protected $item;
    protected $customerOrder;
    protected $customerOrderItem;
    protected $stageLoc;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'email' => '<EMAIL>'
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::factory()->create([
            'whse_num' => 'TEST_WH',
            'whse_status' => 1,
            'site_id' => 1
        ]);

        // Create test location
        $this->location = Loc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'loc_status' => 1,
            'site_id' => 1
        ]);

        // Create catch weight enabled item
        $this->item = Item::factory()->create([
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'item_status' => 1,
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create customer order
        $this->customerOrder = CustomerOrder::factory()->create([
            'co_num' => 'CO_TEST_001',
            'cust_num' => 'TEST_CUST',
            'site_id' => 1
        ]);

        // Create customer order item
        $this->customerOrderItem = CustomerOrderItem::factory()->create([
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'whse_num' => 'TEST_WH',
            'cust_num' => 'TEST_CUST',
            'qty_ordered' => 100.0,
            'qty_shipped' => 50.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create stage location (representing picked items)
        $this->stageLoc = StageLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'stage_num' => 'STAGE_01',
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'item_num' => 'CW_ITEM_001',
            'lot_num' => 'LOT_001',
            'qty_staged' => 50.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create item location with inventory
        ItemLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'qty_on_hand' => 150.0,
            'qty_available' => 150.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create lot location with inventory
        LotLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'lot_num' => 'LOT_001',
            'qty_on_hand' => 150.0,
            'qty_available' => 150.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        $this->actingAs($this->user);
    }

    /** @test */
    public function it_detects_catch_weight_item_and_renders_catch_weight_view()
    {
        $response = $this->get(route('CoUnPickingProcess', [
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'item_num' => 'CW_ITEM_001',
            'stage_num' => 'STAGE_01',
            'item_desc' => 'Catch Weight Test Item'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('shipping.counpick.process_cw');
        $response->assertViewHas('stageLoc');
        $response->assertViewHas('printLabel');
        $response->assertViewHas('allow_over_unpick');
    }

    /** @test */
    public function it_renders_standard_view_for_non_catch_weight_item()
    {
        // Create non-catch weight item
        $normalItem = Item::factory()->create([
            'item_num' => 'NORMAL_ITEM_001',
            'item_desc' => 'Normal Test Item',
            'item_status' => 1,
            'catch_weight' => 0,
            'lot_tracked' => 1,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Update stage location to use normal item
        $this->stageLoc->update(['item_num' => 'NORMAL_ITEM_001']);

        $response = $this->get(route('CoUnPickingProcess', [
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'item_num' => 'NORMAL_ITEM_001',
            'stage_num' => 'STAGE_01',
            'item_desc' => 'Normal Test Item'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('shipping.counpick.process');
    }

    /** @test */
    public function it_processes_catch_weight_co_unpicking_successfully()
    {
        $requestData = [
            'batch_id' => 'TEST_BATCH_001',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001'],
            'arr_qty' => [50.0],
            'arr_expiry_date' => [null],
            'qty_conv' => 50.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => true,
            'qty_staged' => 50.0,
            'qty_on_hand' => 150.0,
            'lot_tracked' => 1,
            'getCheckNONINV' => 0
        ];

        $response = $this->post(route('runCoUnPickCWProcess'), $requestData);

        $response->assertRedirect();
        $response->assertSessionHas('alert.config.type', 'success');

        // Verify inventory was increased (unpicking adds inventory back)
        $itemLoc = ItemLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->first();
        $this->assertEquals(200.0, $itemLoc->qty_on_hand);

        $lotLoc = LotLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->where('lot_num', 'LOT_001')
            ->first();
        $this->assertEquals(200.0, $lotLoc->qty_on_hand);
    }

    /** @test */
    public function it_validates_catch_weight_tolerance()
    {
        // Test with quantity outside tolerance
        $requestData = [
            'batch_id' => 'TEST_BATCH_002',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001'],
            'arr_qty' => [60.0], // This exceeds tolerance (50 + 5% = 52.5)
            'arr_expiry_date' => [null],
            'qty_conv' => 60.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => true,
            'qty_staged' => 50.0,
            'qty_on_hand' => 150.0,
            'lot_tracked' => 1,
            'getCheckNONINV' => 0
        ];

        $response = $this->post(route('runCoUnPickCWProcess'), $requestData);

        $response->assertSessionHasErrors();
    }

    /** @test */
    public function it_handles_multiple_lots_in_single_transaction()
    {
        // Create additional lot location
        LotLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'lot_num' => 'LOT_002',
            'qty_on_hand' => 100.0,
            'qty_available' => 100.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        $requestData = [
            'batch_id' => 'TEST_BATCH_003',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001', 'LOT_002'],
            'arr_qty' => [25.0, 25.0],
            'arr_expiry_date' => [null, null],
            'qty_conv' => 50.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => true,
            'qty_staged' => 50.0,
            'qty_on_hand' => 150.0,
            'lot_tracked' => 1,
            'getCheckNONINV' => 0
        ];

        $response = $this->post(route('runCoUnPickCWProcess'), $requestData);

        $response->assertRedirect();
        $response->assertSessionHas('alert.config.type', 'success');

        // Verify both lots were processed
        $lotLoc1 = LotLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->where('lot_num', 'LOT_001')
            ->first();
        $this->assertEquals(175.0, $lotLoc1->qty_on_hand);

        $lotLoc2 = LotLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->where('lot_num', 'LOT_002')
            ->first();
        $this->assertEquals(125.0, $lotLoc2->qty_on_hand);
    }
}
