<?php

namespace App\Http\Controllers\Outgoing;

use App\LotLoc;
use App\Services\PickShipService;
use App\StageLoc;
use Illuminate\Http\Request;
use App\Item;
use App\Http\Controllers\Controller;
use App\Services\GeneralService;
use App\Services\POService;
use App\View\CoPickView;
use App\ItemLoc;
use App\Loc;
use App\CustomerOrderItem;
use App\StagingLine;
use App\CustomerOrder;
use App\AlternateBarcode;
use App\Container;
use App\ContainerItem;
use App\Services\PalletService;
use App\Services\UOMService;
use Alert;
use DB;
use App\View\TparmView;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use App\Services\SapCallService;
use Exception;
use App\UomConv;
use App\Http\Controllers\ValidationController;
use App\Services\SapApiCallService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use App\Services\CatchWeightService;
use App\SiteSetting;
use Timezone;

class  CoUnPickController extends Controller
{
    private $pickShipService;
    use \App\Traits\HasDefaultLoc;

    public function __construct(PickShipService $pickShipService)
    {
        $this->pickShipService = $pickShipService;
    }
    public function index()
    {
        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');;
        }
        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('CustOrdPicking', 'enable_warehouse');

        $lpnDef = PalletService::getDefaultLpnTransaction('CO UnPicking');

        return view('shipping.counpick.index')->with('tparm', $tparm)->with('lpnDef', $lpnDef);
    }

    public function CoUnPickDetails(Request $request)
    {
        //dd($request);

        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');;
        }
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        // Send error if co_num's status is not open
        $checkCoNum = CustomerOrderItem::where('co_num', $request->co_num)->where('whse_num', $request->whse_num)->where('rel_status', '!=', "C")->exists();
        if (!$checkCoNum) {
            throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num . ' cannot be proceed due to status is completed/closed']);
        }

        // Send error if stage location is not exist
        $checkStageLoc = StageLoc::where('whse_num', $request->whse_num)->where('co_num', $request->co_num)->where('stage_num', $request->stage_num)->exists();
        if (!$checkStageLoc) {
            throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' not exist']);
        }

        // Send error if to location is not active
        $checkToLoc = Loc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->first();
        if ($checkToLoc) {
            if ($checkToLoc->loc_status == 0) {
                throw ValidationException::withMessages(['loc_num' => 'Location-' . $request->stage_num . ' cannot be proceed due to status is inactive']);
            }

            if ($checkToLoc->loc_type == 'T') {
                throw ValidationException::withMessages(['loc_num' => 'Location-' . $request->stage_num . ' cannot be proceed due to Location is a Transit Location']);
            }
        } else {
            throw ValidationException::withMessages(['loc_num' => 'Location-' . $request->stage_num . ' not exist']);
        }

        $getCustNum = CustomerOrderItem::where('co_num', $request->co_num)->where('whse_num', $request->whse_num)->first();
        $co_num = $request->co_num;
        $co_line = $request->co_line;
        $stage_num = $request->get('stage_num');
        if (is_null($stage_num)) {
            $stage_num =  session('stage_num');
        }

        if ($request->item_num == null) {
            $co_list =  \DB::table('stage_locs')
                ->select('stage_locs.*', 'coitems.item_desc', 'coitems.qty_manual_picked')
                ->where('stage_locs.co_num', $co_num)
                ->where('stage_locs.whse_num', $request->whse_num)
                ->where('stage_locs.co_line', 'like', '%' . $co_line . '%')
                ->where('stage_locs.lpn_num', '=', "")
                ->leftJoin('coitems', function ($join) {
                    $join->on('stage_locs.co_num', '=', 'coitems.co_num');
                    $join->on('stage_locs.co_line', '=', 'coitems.co_line');
                    $join->on('stage_locs.co_rel', '=', 'coitems.co_rel');
                    $join->on('stage_locs.item_num', '=', 'coitems.item_num');
                    $join->on('stage_locs.site_id', '=', 'coitems.site_id');
                })
                ->where('stage_locs.stage_num', $stage_num)
                ->where('stage_locs.site_id', auth()->user()->site_id)
                // ->where('coitems.item_num','LIKE',"%$request->item_num%")
                ->where('coitems.item_num', 'LIKE', "$request->item_num")

                ->orderBy('stage_locs.co_num')
                ->get();
        } else {
            $co_list =  \DB::table('stage_locs')
                ->select('stage_locs.*', 'coitems.item_desc', 'coitems.qty_manual_picked')
                ->where('stage_locs.co_num', $co_num)
                ->where('stage_locs.whse_num', $request->whse_num)
                ->where('stage_locs.co_line', 'like', '%' . $co_line . '%')
                ->leftJoin('coitems', function ($join) {
                    $join->on('stage_locs.co_num', '=', 'coitems.co_num');
                    $join->on('stage_locs.co_line', '=', 'coitems.co_line');
                    $join->on('stage_locs.co_rel', '=', 'coitems.co_rel');
                    $join->on('stage_locs.item_num', '=', 'coitems.item_num');
                    $join->on('stage_locs.site_id', '=', 'coitems.site_id');
                })
                ->where('stage_locs.stage_num', $stage_num)
                ->where('stage_locs.site_id', auth()->user()->site_id)
                ->where('coitems.item_num', $request->item_num)
                ->where('stage_locs.lpn_num', '=', NULL)
                ->orderBy('stage_locs.co_num')
                ->get();
        }

        // dd($co_list, $request);

        if ($request->item_num != "" && ($co_list->count() == 1)) {
            $coitem = $co_list->first();
            $request->co_rel = $coitem->co_rel;
            $request->co_line = $coitem->co_line;
            $getItemDesc = \DB::table('items')->where('item_num', $coitem->item_num)->pluck('item_desc')->first();
            $request->item_desc = $getItemDesc;
            //dd($getItemDesc);
            return ($this->CoUnPickingProcess($request));
        }
        if ($request->pick_by == 'pallet') {
            return view('shipping.counpick.newcounpickpallet')->with('co_list', $co_list)->with('co_num', $request->co_num)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num)->with('cust_num', $getCustNum->cust_num)->with('toLoc', $request->toLoc);
        }
        return view('shipping.counpick.colist')->with('co_list', $co_list)->with('co_num', $request->co_num)->with('whse_num', $request->whse_num)->with('item_num', $request->item_num)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('unit_quantity_format', $unit_quantity_format);
    }

    public function CoUnPickingProcess(Request $request)
    {
        //dd($request);

        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');
        }
        $co_num = $request->co_num;
        $co_line = $request->co_line;
        $item_num = $request->item_num;
        $stage_num = $request->stage_num;
        $batch_id = generateBatchId("CustOrdUnPicking");

        if ($request->id == null) {
            $stageLoc = StageLoc::where('co_num', $co_num)
                ->where('co_line', $co_line)
                ->where('item_num', $item_num)
                ->where('stage_num', $stage_num)
                ->first();
        } else {
            $stageLoc = StageLoc::find($request->id);
        }
         //dd($stageLoc->co_num);

// http://127.0.0.1:8000/home/<USER>/co-unpicking/process?_token=vkScvMY4jVYG3AoY6TC1KXUL2gwfQYpqS9C6CDA3&co_line=3&co_num=COTest05092025&co_rel=0&id=649&item_desc=Item05052025lot&item_num=Item05052025lot&qty_req=0.0000&stage_num=Loc18022501&uom=UOM01Test
        if(@$stageLoc==null)
        {
            //dd('jsjsj');
            $url = route('CoUnPick', [

            ]);

return redirect()->to($url)->withErrors([
    'stageLoc' => __('error.mobile.stageLoc_finish_unpicked')
]);



            // Alert::error(__('error.mobile.stageLoc_finish_unpicked'))->persistent('Dismiss');
            // throw ValidationException::withMessages([__('error.mobile.stageLoc_finish_unpicked') ]);
           // dd('jsjsj');
            // return redirect()->back()->with('error',__('error.mobile.stageLoc_finish_unpicked'));

            }

        $coitem = CustomerOrderItem::where('co_num', @$stageLoc->co_num)
        ->where('co_line', $stageLoc->co_line)
        ->where('co_rel', $stageLoc->co_rel)
        ->first();




        $item = Item::select('lot_tracked', 'uom', 'catch_weight', 'catch_weight_tolerance')
            ->where('item_num', $stageLoc->item_num)
            ->first();
        $lot_tracked = @$item->lot_tracked;
        $stageLoc['base_uom'] = @$item->uom;
        $stageLoc['item'] = $item; // Add item relationship for catch weight access
        if ($lot_tracked) {
            //Cant fetch by static, else $this->uom wont work in decimals
            $lotLoc =  LotLoc::where('loc_num', $stage_num)->where('lot_num', $stageLoc->lot_num)->where('whse_num', $stageLoc->whse_num)
                ->where('item_num', $stageLoc->item_num)->first();
            // Condition
            $qty_available = $lotLoc->qty_available  ?? 0;
        } else {
            //Cant fetch by static, else $this->uom wont work in decimals
            $itemLoc = ItemLoc::where('loc_num', $stage_num)->where('whse_num', $stageLoc->whse_num)->where('item_num', $stageLoc->item_num)->first();
            $qty_available = $itemLoc->qty_available ?? 0;
        }
        $item_desc = $request['item_desc'];

        $stageLoc['item_desc'] = $item_desc;
        $stageLoc['qty_on_hand'] = $qty_available;

        $detail = $this->getLocByRankReceipt($stageLoc->whse_num, $stageLoc->item_num);
        // dd($detail);
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if (!$detail) {
            $stageLoc['defloc'] = '';
            $stageLoc['defqty'] = '';
        } else {
            $stageLoc['defloc'] = $detail['loc_num'];
            $stageLoc['defqty'] = $detail['qty_available'];
        }


        // Check the NON-INV 1=NON_INV / Child  0=Normal
        // $getCheckNONINV =  DB::table('coitems_sap_exts')->select('id')->where(
        //     [
        //         ['co_num', '=',  $co_num ],
        //         ['co_line', '=', $co_line],
        //         ['site_id', '=', auth()->user()->site_id]
        //     ]
        //     )->whereNotNull('family_line')->exists();

        if ($item_num == "NON-INV") {
            $getCheckNONINV = 1;
            $stageLoc->base_uom =  $coitem->uom;
        } else {
            $getCheckNONINV = 0;
        }


        // Check if item is catch weight enabled and redirect to catch weight view
        $tparm = new TparmView;
        $printLabel = $tparm->getTparmValue('COUnPick', 'print_label');
        $allow_over_unpick = $tparm->getTparmValue('CustOrdUnPicking', 'allow_over_unpick');

        $view = $item->catch_weight ? 'shipping.counpick.process_cw' : 'shipping.counpick.process';

        return view($view)->with('getCheckNONINV', $getCheckNONINV)->with('cust_num', $coitem->cust_num)->with('stageLoc', $stageLoc)->with('coitem', $coitem)->with('lot_tracked', $lot_tracked)->with('stage_num', $stage_num)->with('qty_available', $qty_available)->with('unit_quantity_format', $unit_quantity_format)->with('batch_id', $batch_id)->with('printLabel', $printLabel)->with('allow_over_unpick', $allow_over_unpick);
    }

    public function unpickCOValidation(Request $request)
    {
        $errors = [];

        if ($request->getCheckNONINV == 0) {
            if ($request['item_num']) {
                $item_num = $request['item_num'];
                $result = ValidationController::checkItemNumValidation($item_num, $request['whse_num']);

                  if ($result !== true) {
                    $errors['item_num'] = $result;
                }
            }

            if ($request['loc_num']) {
                $result = ValidationController::checkTransitPickingLocValidtion($request, 'loc_num', true);
                $tparm = new TparmView;

                if ($result !== true) {
                    $errors['loc_num'] = $result;
                }
            }
        }

        $coitem = new CustomerOrderItem;
        $getCoItem = $coitem->where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->first();

        // Verifying COItem exist
        if (!$getCoItem) {
            $errors['co_num'] = __('error.mobile.notexist', ['resource' => '[' . $request->co_num . '-' . $request->co_line . '-' . $request->co_rel . ']']);
        }

        // Verify status
        if ($getCoItem->rel_status != 'O') {
            $errors['co_num'] = __('error.mobile.status_is_completed', ['resource' => $getCoItem->co_num]);
        }

        $checkStageLoc = StageLoc::where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('whse_num', $request->whse_num)
            ->where('item_num', $request->item_num)
            ->where('stage_num', $request->stage_num)
            ->first();

        if (!$checkStageLoc) {
            $errors['loc_num'] = 'Stage Location-' . $request->stage_num . ' not exist';
        }

        if ($request['batch_id'] && checkBatchIdExists($request['batch_id'])) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);

        }

        return $errors;
    }

    public function UnPickCo(Request $request)
    {
        //dd($request);
        // $request = validateSansentiveValue($request);
        // Skip NON-INV Item
        // if ($request->getCheckNONINV == 0) {
            // $request->validate([
            //     'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
            // ], [
            //     'loc_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
            // ]);
        // }

        $validateErrors = self::unpickCOValidation($request);

        if (count($validateErrors) > 0) {
            return back()->withErrors($validateErrors)->withInput();
        }

        //dd($request);
        $coitem = new CustomerOrderItem;
        $getCoItem = $coitem->where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->first();
            $tparm = new TparmView;
            $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

            if($sap_require_check_online==1 && $sap_trans_order_integration==1){
                // Checking SAP Server
                $site_id = auth()->user()->site_id;
                $checkConnection = SapApiCallService::getSQPServerConnection($site_id,null,'Job Receipt',1);

                if($checkConnection > 2 ){
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                    return redirect()->back();
                }

            }

        // // Verifying COItem exist
        // if (!$getCoItem) {
        //     throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->co_num . '-' . $request->co_line . '-' . $request->co_rel . ']'])]);
        // }

        // // Verify status
        // if ($getCoItem->rel_status != 'O') {
        //     throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => $getCoItem->co_num])]);
        // }

        // Verify stage loc
        $checkStageLoc = StageLoc::where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('whse_num', $request->whse_num)
            ->where('item_num', $request->item_num)
            ->where('stage_num', $request->stage_num)
            ->first();

        // if (!$checkStageLoc) {
        //     throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' not exist']);
        // }

        $item = Item::select('uom')->where('item_num', $request->item_num)->first();

        // Rewrite the ReadOnly fields
        $request['co_num'] = $getCoItem->co_num;
        $request['co_line'] = $getCoItem->co_line;
        $request['co_rel'] = $getCoItem->co_rel;
        $request['cust_num'] = $getCoItem->cust_num;
        $request['whse_num'] = $getCoItem->whse_num;
        $request['item_num'] = $getCoItem->item_num;
        $request['stage_num'] = $checkStageLoc->stage_num;
        $request['qty_staged'] = $checkStageLoc->qty_staged;
        if ($request->getCheckNONINV == 0) {
            $request['base_uom'] = $item->uom;
            $request['original_loc_num'] =  $request->loc_num;
            // $request['uom'] = $item->uom;
        } else {
            $request['base_uom'] = $getCoItem->uom;
            $request['original_loc_num'] =  $checkStageLoc->stage_num;
            $request['loc_num'] =  $checkStageLoc->stage_num;
            // $request['uom'] = $getCoItem->uom;
        }
        $record['pick_uniqekey'] = base64_encode($request->whse_num . $request->stage_num . $request->co_num . $request->co_line) ?? NULL;
        $lockKey = 'insert_lock_' . $record['pick_uniqekey'];
        $lock = Cache::lock($lockKey, 1); // Lock for 10 seconds


        $transType = config('constants.modules.CustOrdShipping');



      if ($lock->get()) {

                    DB::table('staging_lines')->lockForUpdate();
                    DB::table('malt_trans')->lockForUpdate();
                    DB::table('item_locs')->lockForUpdate();
                    DB::table('stage_locs')->lockForUpdate();
                    DB::table('lot_locs')->lockForUpdate();  // dd($request);
        $coPickRequest = $this->pickShipService->executeCoUnPick($request);
            $pickuniqkey = base64_encode($request->whse_num . $request->stage_num . $request->co_num . $request->co_line);

         if ($coPickRequest) {
                        $created_date = StagingLine::where('pick_uniqekey', $pickuniqkey)->where('whse_num', $request->whse_num)->where('to', $request->stage_num)->where('site_id', auth()->user()->site_id)->orderby('id', 'DESC')->first();

                       // dd($created_date,$pickuniqkey,$request);
                        // $datetimeWithAddedSeconds = $created_date->created_date->addSeconds(10);
                        // $currentDatetime = Carbon::now();

                        // if ($datetimeWithAddedSeconds->gt($currentDatetime)) {
                        //     //if ($currentDatetime->gt($datetimeWithAddedSeconds)) {
                        //     throw ValidationException::withMessages(['This record concurrent posting. Please try again later']);
                        // }

                        //dd($currentDatetime, $datetimeWithAddedSeconds);
                    }



        Alert::success('Success', __('success.processed', ['process' => __('Customer Order UnPicking')]))->persistent('Close');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        if ($sap_trans_order_integration == 1) {
            /*$item_uom = Item::where('item_num', $request->item_num)->pluck('uom')->first();
            if( $item_uom != $request->uom )
            {
                // Find the conv UOM
                $item = $request->item_num;
                $converted_uom =  $request->uom;
                $baseuom= $item_uom ;
                $resp = UomConv::select('conv_factor')->where('uom_from', $baseuom)->where('uom_to', $converted_uom)->where('item_num',$item)->where('site_id',auth()->user()->site_id)->pluck('conv_factor')->first();
            }
            else{
                $resp = 1;
            }
            $request->merge([
                'qty_conv' => $request->qty/$resp,
            ]);*/


            if (@$request->item_num != "NON-INV") {
                if ($request->lot_num != "") {
                    $baseuom = LotLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('item_num', $request->item_num)->value('uom');
                } else {
                    $baseuom = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->value('uom');
                }
            } else {
                $baseuom = $request->co_uom;
            }
            $type_mode = __('mobile.nav.co_picking');
            $lineuom = CustomerOrderItem::where('co_num', $request->co_num)->where('co_line', $request->co_line)->value('uom');
            $cust_num = $request->cust_num;
            $selectuom = $request->uom;

            $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, $cust_num, '', $type_mode);

            $base_qty = $getQtyConv['conv_qty_to_base']['qty'];

            $request->merge([
                'qty_conv' => $base_qty,
            ]);






            //  $result = SapCallService::postCOUnPicking($request);
            //   if(config('icapt.enable_sap_ap_readfrom_maltrans'))
            //   {
            //     $result = SapCallService::postStockTransferFromMaltrans('CO UnPick');
            //     if($result!=200)
            //         {
            //         Alert::error('Error', $result)->persistent('Dismiss');
            //         }
            //   }
            //   else
            //   {
            //     $result = SapCallService::postCOUnPicking($request);
            //   }
            if ($request->getCheckNONINV == 0) {
                // if(config('icapt.enable_sap_ap_readfrom_maltrans'))
                //{
                // Later Change to read from Matltrans
                if($sap_single_bin==1){
                    $result = 200;
                }
                else{
                $result = SapCallService::postStockTransferFromMaltrans('CO UnPick', $request);

                //}
                // else
                // {
                //     $result = SapCallService::postCOUnPicking($request);
                // }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
             }
            }
        }
    }
    else{
throw ValidationException::withMessages(['Another user is currently editing this record. Please try again later']);
    }


        Session::put('modulename', 'COUnPick');
        Session::put('co_num', $request->co_num);
        Session::put('whse_num', $request->whse_num);
        Session::put('stage_num', $request->stage_num);

        return app('App\Http\Controllers\RouteController')->gotoCOUnPick();
    }

    public function runCoUnPickCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate CO and Stage Location
        $co_num = $request->ref_num;
        $co_line = $request->ref_line;
        $stage_num = $request->stage_num;

        if (empty($co_num) || empty($co_line) || empty($stage_num))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('Customer Order') ]) ]);
        }

        $stageLoc = StageLoc::where('co_num', $co_num)
            ->where('co_line', $co_line)
            ->where('stage_num', $stage_num)
            ->first();

        if (!$stageLoc)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('Stage Location') ]) ]);
        }

        // Get Tolerance and UOM - for CO unpicking, tolerance is the qty staged
        $tolerance = $stageLoc->qty_staged;
        $tolerance_uom = $stageLoc->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $siteSettings = new SiteSetting();
        $this->timezone = $siteSettings->getTimezone();
        $now =  Timezone::convertFromUTC(now(), $this->timezone, SiteSetting::getOutputDateFormat() .' H:i:s');

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        DB::beginTransaction();

        try {
            $transType = config('constants.modules.CustOrdShipping');

            // Calculate total quantity from all lots
            $total_qty = array_sum($request->arr_qty ?? []);

            // Process each lot individually
            foreach ($request->arr_lot_num as $key => $lot_num) {
                $qty = $request->arr_qty[$key];
                $expiry_date = $request->arr_expiry_date[$key] ?? null;

                // Create individual request for each lot
                $lotRequest = new Request();
                $lotRequest->merge([
                    'whse_num' => $request->whse_num,
                    'loc_num' => $request->loc_num,
                    'item_num' => $request->item_num,
                    'lot_num' => $lot_num,
                    'qty' => $qty,
                    'qty_conv' => $qty,
                    'uom' => $request->base_uom,
                    'uom_conv' => $request->base_uom,
                    'ref_num' => $co_num,
                    'ref_line' => $co_line,
                    'co_num' => $co_num,
                    'co_line' => $co_line,
                    'co_rel' => $request->co_rel,
                    'cust_num' => $request->cust_num,
                    'stage_num' => $stage_num,
                    'trans_type' => 'Co UnPick From',
                    'batch_id' => $batch_id,
                    'expiry_date' => $expiry_date,
                    'getCheckNONINV' => $request->getCheckNONINV ?? 0,
                    'qty_staged' => $request->qty_staged,
                    'qty_on_hand' => $request->qty_on_hand,
                    'base_uom' => $request->base_uom,
                    'lot_tracked' => $request->lot_tracked
                ]);

                if ($sap_trans_order_integration != 1) {
                    // Update inventory and create material transactions for catch weight
                    // For CO unpicking, we need to add inventory back (positive quantity - reverse of picking)
                    $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($lotRequest, $tolerance_uom, $transType, "CO UnPick");

                    // Execute CO unpick process for this lot
                    $coUnPickRequest = $this->pickShipService->executeCoUnPick($lotRequest);
                }
            }

            DB::commit();

            Alert::success('Success', __('success.processed', ['process' => __('Customer Order UnPicking - Catch Weight')]));

            // Redirect back to CO unpicking list
            return redirect()->route('CoUnPickDetails', [
                'whse_num' => $request->whse_num,
                'co_num' => $co_num,
                'stage_num' => $stage_num
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function CoUnpickList(Request $request)
    {

        // dd($request, 'testcounpick');
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $co_num = $request->get('co_num');
            $whse_num = $request->get('whse_num');
            $co_line = $request->get('co_line');
            $stage_num = $request->get('stage_num');
            $item_num = $request->get('item_num');

            if ($query != '') {
                $coitem = new CustomerOrderItem();
                $data = $coitem->coItemSearchList($co_num, $whse_num, $query);
            } else {
                $data =

                    // CoPickView::where('co_num',$co_num)
                    //     ->where('rel_status','!=','C')
                    //     ->select('co_num',  'co_line', 'co_rel', 'item_num', 'item_desc', 'uom', 'qty_released', 'qty_shipped', 'qty_returned', 'qty_shortage', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                    //     ->where('co_line', 'like', '%'. $request->co_line .'%')
                    //     ->where('item_num', 'like', '%'. $item_num .'%')
                    //     ->orderBy('co_num')
                    //     ->groupBy('co_line')
                    //     ->groupBy('co_rel')
                    //     ->get();

                    DB::table('stage_locs')
                    ->select('stage_locs.*', 'coitems.item_desc', 'coitems.co_line', 'coitems.qty_picked', 'coitems.qty_manual_picked', 'coitems.qty_shortage')
                    ->where('stage_locs.co_num', $co_num)
                    ->where('stage_locs.whse_num', $request->whse_num)
                    ->where('stage_locs.co_line', 'like', '%' . $request->co_line . '%')
                    ->where('stage_locs.lpn_num', '=', NULL)
                    ->leftJoin('coitems', function ($join) {
                        $join->on('stage_locs.co_num', '=', 'coitems.co_num');
                        $join->on('stage_locs.co_line', '=', 'coitems.co_line');
                        $join->on('stage_locs.co_rel', '=', 'coitems.co_rel');
                        $join->on('stage_locs.item_num', '=', 'coitems.item_num');
                        $join->on('stage_locs.site_id', '=', 'coitems.site_id');
                    })
                    ->where('stage_locs.stage_num', $request->stage_num)
                    ->where('stage_locs.site_id', auth()->user()->site_id)
                    ->where('coitems.item_num', 'LIKE', "%$item_num%")

                    ->orderBy('stage_locs.co_num')
                    ->orderByRaw('cast(coitems.co_line as unsigned) ASC')
                    ->get();
            }
            //dd($data);
            $total_row = $data->count();
            // <td><label>' . __('mobile.label.qty_required') . ' </label></td>
            // <td>
            //     <span class="form-control border-primary pseudoinput">' . number_format($row->qty_shortage, $unit_quantity_format, '.', '') . ' </span>


            if ($total_row > 0) {
                foreach ($data as $row) {
                    // if($row->item_num=="NON-INV"){
                    //     continue;
                    // }
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                    // if (is_null($row->qty_staged)) $row->qty_staged = 0;
                    // if ($row->qty_required - $row->qty_staged > 0) {
                    $output .= '
                            <form class="form" autocomplete="off" id="myform" method="GET" action="/home/<USER>/co-unpicking/process">
                                <div class="row border  border-primary" id="mybox" onclick="javascript:this.parentNode.submit();" >
                                <input type="hidden" name="_token" value="' . csrf_token() . '">
                                    <div class="col-xs-12">
                                        <table style="width: 100%;">
                                        <input type="hidden" value="' . $row->id . '" name="id">
                                            <input type="hidden" value="' . $stage_num . '" name="stage_num">
                                            <input type="hidden" value="' . $row->co_num . '" name="co_num">
                                            <tr>
                                                <td>
                                                    <label for="co_line">' . __('mobile.label.co_line') . '</label>
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->co_line . ' </span>
                                                    <input type="hidden" id="co_line" class="form-control border-primary" value="' . $row->co_line . '" readonly>
                                                    <input type="hidden" name="co_line" class="form-control border-primary" value="' . $row->co_line . '">
                                                    <input type="hidden" name="co_rel" class="form-control border-primary" value="' . $row->co_rel . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="item_num">' . __('mobile.label.item_num') . '</label>
                                                </td>
                                                <td colspan="4">';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                                    <span class="form-control border-primary pseudoinput">' . $row->item_num . ' </span>
                                                    <input readonly type="hidden" style="text-align:left;" name="item_num"class="form-control border-primary" value="' . $row->item_num . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="70px"></td>
                                                <td colspan="4">
                                                    <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                                    <input name="item_desc" readonly type="hidden" style="text-align:left;"  class="form-control border-primary" value="' . $row->item_desc . '">
                                                </td>
                                            </tr>
                                            <tr>


                                                    <td><label>' . __('mobile.label.qty_picked') . ' </label></td>
                                                    <td>
                                                        <span class="form-control border-primary pseudoinput" style="text-align:right;" >' . numberFormatPrecision($row->qty_staged, $unit_quantity_format, '.', '') . ' </span>


                                                    <input readonly size="10" type="hidden" class="form-control border-primary" name="qty_req" style="text-align:right;" value="' . numberFormatPrecision($row->qty_shortage, $unit_quantity_format, '.', '') . '">
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                                    <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>

                                            <tr>
                                            <td width="70px">' . __('mobile.label.lot_num') . ' </label></td>
                                            <td colspan="4">
                                                <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . @$row->lot_num . '</textarea>

                                            </td>
                                        </tr>


                                        </table>
                                    </div>
                                </div>
                            </form>
                        ';
                    // }
                }
            } else {
                $output = "
                    <tr>
                    <td align='center' colspan='4'>Record not found</td>
                    </tr>
                ";
            }

            $data = array(
                'table_data'  => $output,
                'total_data'  => $total_row
            );

            // dd($data);

            echo json_encode($data);
        }
    }

    // Process of co unpick by pallet
    public function UnpickCobyPalletProcess(Request $request)
    {
        //dd($request);
        // Get Pallet Loc
        DB::beginTransaction();
        try {
            $stageLoc = $request->stage_loc;
            $lpnNum = $request->lpn_num_field;
            $getTotalLineCo = CustomerOrderItem::where('co_num', base64_decode($request->co_num))->count();
            $getPalletLoc = Container::where('lpn_num', $request->lpn_num_field)->first();

            $getPalletLiners = ContainerItem::where('lpn_num', $request->lpn_num_field)->get();
            // $arrStirePalletDetails = array();
            // foreach($getPalletLiners as $keyPallet)
            // {
            //  $arrStirePalletDetails[$keyPallet->lpn_line] = $keyPallet;

            // }
            // dd($request,"dcdcd");
            for ($i = 1; $i <= $request->count; $i++) {
                $co_line = 'ref_line_' . $i;
                $co_rel = 'ref_rel_' . $i;
                $lpn_line = 'lpn_line_' . $i;
                $item = 'item_' . $i;
                $item = utf8_encode(base64_decode($request->$item));
                $item = htmlspecialchars_decode($item);
                $item_desc = 'item_desc_' . $i;
                $uom = 'qty_transact_uom_' . $i;
                $qty_staged = 'qty_transact_' . $i;
                $qty_to_pick = 'qty_input_' . $i;
                $uom_to_pick = 'qty_input_uom_' . $i;

                $lot_num = 'lot_num_' . $i;
                $palletLoc = $getPalletLoc->loc_num;

                $each_lpn_line = explode(",", $request->$lpn_line);

                // check stage loc freeze
                $check_stage_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_loc)->where('item_num', $item)->value('freeze');
                if ($check_stage_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $request->stage_loc])]);
                }

                // check lpn loc freeze
                $check_to_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->to_loc)->where('item_num', $item)->value('freeze');
                if ($check_to_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $request->to_loc])]);
                }

                //Get Item qty bal
                $itemQty = ItemLoc::select('qty_available')->where('whse_num', $request->whse_num)->where('item_num', $item)->where('loc_num', $getPalletLoc->loc_num)->first();

                // if(isset($arrStirePalletDetails[$i]))
                // {
                //dd("999aa",$each_lpn_line,count($each_lpn_line));
                if (count($each_lpn_line) > 1) {
                    foreach ($each_lpn_line as $each_line) {
                        $lpn_each_qty = 'lpn_each_qty' . $each_line;
                        $record[$i][$each_line] =
                            [
                                'whse_num' => $request->whse_num,
                                'stage_loc' => $stageLoc,
                                'co_num' => $request->co_num,
                                'co_line' => $request->$co_line,
                                'co_rel' => $request->$co_rel,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'loc_num' => $getPalletLoc->loc_num,
                                'lot_num' => $request->$lot_num,
                                'item_num' => $item,
                                'item_desc' => $request->$item_desc,
                                'qty_req' => $request->$qty_staged,
                                'base_uom' => $request->$uom,
                                'qty_input' => $request->$lpn_each_qty,
                                'uom' => $request->$uom_to_pick,
                                'to_loc' => $request->to_loc,
                                'each_lpn_line' => $each_line ?? $i,
                            ];

                        $coPick[$i][$each_line] =
                            [
                                'site_id' => auth()->user()->site_id,
                                'whse_num' => $request->whse_num,
                                'co_num' => $request->co_num,
                                'co_line' => $request->$co_line,
                                'co_rel' => $request->$co_rel,
                                'item_num' => $item,
                                'qty_staged' => $request->$qty_staged,
                                'qty_staged_conv' => $request->$qty_to_pick,
                                'stage_num' => $getPalletLoc->loc_num,
                                'qty_available' => $itemQty->qty_available,
                                'qty_available_conv' => $itemQty->qty_available,
                                'base_uom' => $request->$uom,
                                'citem' => $request->$item,
                                'loc_num' => $request->to_loc,
                                'qty_input' => $request->$lpn_each_qty,
                                'uom' => $request->$uom_to_pick,
                                'lpn_num' => $lpnNum,
                                'lot_num' => $request->$lot_num,
                                'created_by' => auth()->user()->name,
                                'modified_by' => auth()->user()->name,
                                'each_lpn_line' => $each_line ?? $i,
                            ];
                    }
                } else {
                    $record[$i] =
                        [
                            'whse_num' => $request->whse_num,
                            'stage_loc' => $stageLoc,
                            'co_num' => $request->co_num,
                            'co_line' => $request->$co_line,
                            'co_rel' => $request->$co_rel,
                            'lpn_num' => $lpnNum,
                            'lpn_line' => $request->$lpn_line,
                            'loc_num' => $getPalletLoc->loc_num,
                            'lot_num' => $request->$lot_num,
                            'item_num' => $item,
                            'item_desc' => $request->$item_desc,
                            'qty_req' => $request->$qty_staged,
                            'base_uom' => $request->$uom,
                            'qty_input' => $request->$qty_to_pick,
                            'uom' => $request->$uom_to_pick,
                            'to_loc' => $request->to_loc,
                            'each_lpn_line' => $each_line ?? $i,
                        ];

                    $coPick[$i] =
                        [
                            'site_id' => auth()->user()->site_id,
                            'whse_num' => $request->whse_num,
                            'co_num' => $request->co_num,
                            'co_line' => $request->$co_line,
                            'co_rel' => $request->$co_rel,
                            'item_num' => $item,
                            'qty_staged' => $request->$qty_staged,
                            'qty_staged_conv' => $request->$qty_to_pick,
                            'stage_num' => $getPalletLoc->loc_num,
                            'qty_available' => $itemQty->qty_available,
                            'qty_available_conv' => $itemQty->qty_available,
                            'base_uom' => $request->$uom,
                            'citem' => $request->$item,
                            'loc_num' => $request->to_loc,
                            'qty_input' => $request->$qty_to_pick,
                            'uom' => $request->$uom_to_pick,
                            'lpn_num' => $lpnNum,
                            'lot_num' => $request->$lot_num,
                            'created_by' => auth()->user()->name,
                            'modified_by' => auth()->user()->name,
                            'each_lpn_line' => $each_line ?? $i,
                        ];
                }
                // }
            }
            // dd($coPick,$record);
            $sendResponseFrom = CoUnPickController::processPalletCoTrans(config('icapt.transtype.co_unpick_from'), $record);
            $sendResponseTo = CoUnPickController::processPalletCoTrans(config('icapt.transtype.co_unpick_to'), $record);
            $updateCo = CoUnPickController::palletCoProcess($coPick);
            $updatePalletLoc = PalletService::updatePalletLoc($lpnNum, $request->to_loc, '', 'CO Unpick');

            DB::commit();

            // SAP Intergration
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            Alert::success('Success', __('success.processed', ['process' => __('Customer Order UnPicking')]))->persistent('Close');
            // && ( $getPalletLoc->loc_num!= $stageLoc)
            if ($sap_trans_order_integration == 1) {
                $lpnnum = $request->lpn_num_field;
                // dd($lpnnum);
                $result = SapCallService::postPalletLPN($lpnnum, 'CO Unpick');
                //    if(config('icapt.enable_sap_ap_readfrom_maltrans'))
                //    {
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
                // }
            }


            Session::put('modulename', 'CoUnPickHome');
            Session::put('co_num', $request->co_num);
            Session::put('whse_num', $request->whse_num);
            Session::put('stage_num', $request->stage_num);

            return app('App\Http\Controllers\RouteController')->gotoCOUnPickHome();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function processPalletCoTrans($transtype, $record)
    {
        $recod_key = array_keys($record);
        // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();
        // "whse_num" => "MAINWH 26/02/2023"
        // "stage_loc" => "Location 01"
        // "co_num" => "FA-170008"
        // "co_line" => "1"
        // "co_rel" => "0"
        // "lpn_num" => "FA-170008"
        // "lpn_line" => "1"
        // "loc_num" => "Location 01"
        // "lot_num" => "1"
        // "item_num" => "FA-170008"
        // "item_desc" => "FA-170002"
        // "qty_req" => "200.00"
        // "base_uom" => "UOM260223"
        // "qty_input" => "200.00"
        // "uom" => "UOM260223"
        // "to_loc" => "Loc 26022304"
        // "lpn_num" => "FA-170008"
        // "lpn_line" => "1"
        // "lot_num" => "1"
        // "item_num" => "FA-170008"
        // "qty_contained" => "100.00000"
        // "qty_allocated" => "0.00"
        // "uom" => "UOM260223"
        // dd($record, $getLpnDetailsQTY);
        // $arrStoreData = array();
        // $intIndex = 1;
        //dd($getLpnDetailsQTY,$record[$intIndex]['co_line']);
        // foreach($record as $dataDatails)
        // {
        //     $arrStoreData[$intIndex]['whse_num'] = $record[$intIndex]['whse_num'];
        //     $arrStoreData[$intIndex]['co_num'] = $record[$intIndex]['co_num'];
        //     $arrStoreData[$intIndex]['co_line'] = $record[$intIndex]['co_line'];
        //     $arrStoreData[$intIndex]['lpn_num'] = $record[$intIndex]['lpn_num'];
        //     $arrStoreData[$intIndex]['lpn_line'] = $record[$intIndex]['lpn_line'];
        //     $arrStoreData[$intIndex]['stage_loc'] = $record[$intIndex]['stage_loc'];
        //     $arrStoreData[$intIndex]['to_loc'] = $record[$intIndex]['to_loc'];
        //     $arrStoreData[$intIndex]['lot_num'] = $record[$intIndex]['lot_num'];
        //     $arrStoreData[$intIndex]['item_num'] = $record[$intIndex]['item_num'];

        //     $arrStoreData[$intIndex]['item_desc'] = $record[$intIndex]['item_desc'];
        //     $arrStoreData[$intIndex]['qty_req'] = $record[$intIndex]['qty_req'];
        //     $arrStoreData[$intIndex]['base_uom'] = $record[$intIndex]['base_uom'];
        //     $arrStoreData[$intIndex]['qty_input'] = $record[$intIndex]['qty_input'];
        //     $arrStoreData[$intIndex]['uom'] = $record[$intIndex]['uom'];


        //     $intIndex++;
        // }

        $arrStoreData = array();
        $intIndex = 1;
        $intLpnIndex = 1;

        foreach ($record as $dataDatails) {
            //check if contains more than 1 lpn line
            if (count($dataDatails) > 1) {
                //foreach($dataDatails as $key => $lpnDet){
                // dd($dataDatails,$lpnDet['whse_num']);
                $arrStoreData[$intLpnIndex]['whse_num'] = $dataDatails['whse_num'];
                $arrStoreData[$intLpnIndex]['co_num'] = $dataDatails['co_num'];
                $arrStoreData[$intLpnIndex]['co_line'] = $dataDatails['co_line'];
                $arrStoreData[$intLpnIndex]['lpn_num'] = $dataDatails['lpn_num'];
                $arrStoreData[$intLpnIndex]['lpn_line'] = $dataDatails['each_lpn_line'];
                $arrStoreData[$intLpnIndex]['from_loc'] = $dataDatails['loc_num'];
                $arrStoreData[$intLpnIndex]['stage_loc'] = $dataDatails['stage_loc'];
                $arrStoreData[$intLpnIndex]['to_loc'] = $dataDatails['to_loc'];
                $arrStoreData[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->where('lpn_line', $dataDatails['each_lpn_line'])->value('lot_num') ?? '';
                $arrStoreData[$intLpnIndex]['item_num'] = $dataDatails['item_num'];

                $arrStoreData[$intLpnIndex]['item_desc'] = $dataDatails['item_desc'];
                $arrStoreData[$intLpnIndex]['qty_req'] = $dataDatails['qty_req'];
                $arrStoreData[$intLpnIndex]['base_uom'] = $dataDatails['base_uom'];
                $arrStoreData[$intLpnIndex]['qty_input'] = $dataDatails['qty_input'];
                $arrStoreData[$intLpnIndex]['uom'] = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->where('lpn_line', $dataDatails['each_lpn_line'])->value('uom') ?? '';

                $intLpnIndex++;
                // }
            } else {
                // Check LPN Line
                $getLPNCount = explode(",", $dataDatails['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {

                    $arrStoreData[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $arrStoreData[$intLpnIndex]['co_num'] = $record[$intIndex]['co_num'];
                    $arrStoreData[$intLpnIndex]['co_line'] = $record[$intIndex]['co_line'];
                    $arrStoreData[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $arrStoreData[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $arrStoreData[$intLpnIndex]['from_loc'] = $record[$intIndex]['loc_num'];
                    $arrStoreData[$intLpnIndex]['stage_loc'] = $record[$intIndex]['stage_loc'];
                    $arrStoreData[$intLpnIndex]['to_loc'] = $record[$intIndex]['to_loc'];
                    $arrStoreData[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $arrStoreData[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];

                    $arrStoreData[$intLpnIndex]['item_desc'] = $record[$intIndex]['item_desc'];
                    $arrStoreData[$intLpnIndex]['qty_req'] = $record[$intIndex]['qty_req'];
                    $arrStoreData[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $arrStoreData[$intLpnIndex]['qty_input'] = $record[$intIndex]['qty_input'];
                    $arrStoreData[$intLpnIndex]['uom'] = $getLpnDetail->uom;

                    $intLpnIndex++;
                }
            }
            $intIndex++;
        }

        foreach ($arrStoreData as $datas) {
            $transData = new Request($datas);
            $sendResponse = PalletService::palletMatlTrans($transtype, $transData);
        }
        return 'true';
    }

    public function palletCoProcess($record)
    {

        $recod_key = array_keys($record);

        // dd($record,$getLpnDetailsQTY);
        // "whse_num" => "MAINWH 26/02/2023"
        // "co_num" => "FA-170008"
        // "co_line" => "1"
        // "co_rel" => "0"
        // "item_num" => "FA-170008"
        // "qty_staged" => "200.00"
        // "qty_staged_conv" => "200.00"
        // "stage_num" => "Location 01"
        // "qty_available" => "0.00000"
        // "qty_available_conv" => "0.00000"
        // "base_uom" => "UOM260223"
        // "citem" => "FA-170008"
        // "loc_num" => "Loc 26022304"
        // "qty" => "200.00"
        // "uom" => "UOM260223"
        // "lpn_num" => "FA-170008"
        // "lot_num" => "1"
        // "lpn_num" => "FA-170008"
        // "lpn_line" => "1"
        // "lot_num" => "1"
        // "item_num" => "FA-170008"
        // "qty_contained" => "100.00000"
        // "qty_allocated" => "0.00"
        // "uom" => "UOM260223"
        $index = 0;
        //  dd($record);
        foreach ($record as $dataline) {
            //check if contains more than 1 lpn line
            if (count($dataline) > 1) {
                // foreach($dataline as $key => $lpnlineDet){

                $recordMashup[$index]['site_id'] = $dataline['site_id'];
                $recordMashup[$index]['co_num'] = $dataline['co_num'];
                $recordMashup[$index]['co_line'] = $dataline['co_line'];
                $recordMashup[$index]['co_rel'] = $dataline['co_rel'];
                $recordMashup[$index]['item_num'] = $dataline['item_num'];
                $recordMashup[$index]['qty_staged_co'] = $dataline['qty_staged'];
                $recordMashup[$index]['qty_staged_lpn'] = $dataline['qty_input'];
                $recordMashup[$index]['whse_num'] = $dataline['whse_num'];
                $recordMashup[$index]['lot_num'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['each_lpn_line'])->value('lot_num') ?? '';
                $recordMashup[$index]['base_uom'] = $dataline['uom'];
                $recordMashup[$index]['uom'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['each_lpn_line'])->value('uom') ?? '';
                $recordMashup[$index]['created_by'] = $dataline['created_by'];
                $recordMashup[$index]['modified_by'] = $dataline['modified_by'];
                $recordMashup[$index]['stage_num'] =  $dataline['stage_num'];
                $recordMashup[$index]['loc_num'] = $dataline['loc_num'];
                $recordMashup[$index]['lpn_num'] = $dataline['lpn_num'];
                $index++;
                // }
            } else {
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('item_num', $dataline['item_num'])->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetails) {
                    $recordMashup[$index]['site_id'] = $dataline['site_id'];
                    $recordMashup[$index]['co_num'] = $dataline['co_num'];
                    $recordMashup[$index]['co_line'] = $dataline['co_line'];
                    $recordMashup[$index]['co_rel'] = $dataline['co_rel'];
                    $recordMashup[$index]['item_num'] = $dataline['item_num'];
                    $recordMashup[$index]['qty_staged_co'] = $dataline['qty_staged'];
                    $recordMashup[$index]['qty_staged_lpn'] = $dataline['qty_input'];
                    $recordMashup[$index]['whse_num'] = $dataline['whse_num'];
                    $recordMashup[$index]['lot_num'] = $getLpnDetails->lot_num;
                    $recordMashup[$index]['base_uom'] = $dataline['uom'];
                    $recordMashup[$index]['uom'] = $getLpnDetails->uom;
                    $recordMashup[$index]['created_by'] = $dataline['created_by'];
                    $recordMashup[$index]['modified_by'] = $dataline['modified_by'];
                    $recordMashup[$index]['stage_num'] =  $dataline['stage_num'];
                    $recordMashup[$index]['loc_num'] = $dataline['loc_num'];
                    $recordMashup[$index]['lpn_num'] = $dataline['lpn_num'];
                    $index++;
                }
            }
        }
        // dd($recordMashup);
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $total_record = count($record);
        $check = 0;
        $rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($record[1]['whse_num'] ?? $record[1][1]['whse_num']), base64_encode($record[1]['item_num'] ?? $record[1][1]['item_num']), '');
        foreach ($recordMashup as $datas) {
            $transData = new Request($datas);
            $transData = validateSansentiveValue($transData);
            $transData['ref_num'] = $transData->co_num;
            $transData['ref_line'] = $transData->co_line;
            $transData['ref_release'] = $transData->co_rel;
            $transData['qty'] = $transData->qty;
            $requestTo = clone $transData;
            $datas['qty_staged'] = $transData->qty_staged_co;
            $transData['qty_staged'] = $transData->qty_staged_lpn;
            // dd($transData);
            $type_mode = __('mobile.nav.co_picking');
            $cust_num = CustomerOrder::where('co_num', $transData->co_num)->value('cust_num');
            $selectuom = $transData->uom;
            $baseuom = Item::where('item_num', $transData->item_num)->value('uom');

            $lineuom = CustomerOrderItem::where('co_num', $transData->co_num)->where('co_line', $transData->co_line)->value('uom');
            $getQtyConv = UomConv::convertUOM($baseuom, $lineuom, $selectuom, $transData->qty_staged, $transData->item_num, $cust_num, '', $type_mode);
            // dd($baseuom, $selectuom, $lineuom, $transData->qty_staged, $transData->item_num, $cust_num, '', $type_mode, $getQtyConv);
            // Conversion for CO item
            // $co_qty = UOMService::convertCORequest($request);

            $co_qty = $getQtyConv['conv_qty_to_base']['qty'];
            $uom_conv = $getQtyConv['conv_qty_to_base']['uom'];
            $co_line_qty = $getQtyConv['conv_qty_to_line']['qty'];
            $line_uom_conv = $getQtyConv['conv_qty_to_line']['uom'];

            $fromArr = [
                'whse_num' => $datas['whse_num'],
                'loc_num' => $datas['stage_num'],
                'item_num' =>  $datas['item_num'],
                'qty_conv' => $co_qty,
                'lot_num'  => $datas['lot_num']

            ];


            $updateFromItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr = [], 0, 'From CO UnPicking');


            $toArr = [
                'whse_num' => $datas['whse_num'],
                'loc_num'  => $datas['loc_num'],
                'item_num' =>  $datas['item_num'],
                'qty_conv' => $co_qty,
                'lot_num'  => $datas['lot_num'],
                'uom_conv' =>  $uom_conv,
                'rank' =>  $rank

            ];

            $updateToItemLocation = PalletService::updateItemLocLotQty($fromArr = [], $toArr, 0, 'To CO UnPicking');

            // Update table: coitems
            $coitem = CustomerOrderItem::where('co_num', $transData->co_num)
                ->where('co_line', $transData->co_line)
                ->where('co_rel', $transData->co_rel)
                ->first();
            if ($coitem) {
                $coitem->qty_picked = number_format($coitem->qty_picked - $transData->qty_staged, $unit_quantity_format, '.', '');
                $coitem->qty_manual_picked = number_format($coitem->qty_manual_picked - $transData->qty_staged, $unit_quantity_format, '.', '');
                $coitem->save();
            }

            $requestTo['qty_conv'] = $transData->qty_staged;
            $requestTo['uom_conv'] = $line_uom_conv;
            // dd($getQtyConv);
            // $stageLoc = StageLoc::createFromRequest(UOMService::convertToStageQty($requestTo));
            $stageLoc = StageLoc::createFromRequest($requestTo);
            //$coPickRequest = $this->pickShipService->executeCoUnPick($requestTo);
            $stageLocRecord = $this->pickShipService->updateStageLocdeduct($stageLoc);

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            if ($sap_trans_order_integration == 1) {
                $item_uom = Item::where('item_num', $transData->item_num)->pluck('uom')->first();
                if ($item_uom != $transData->uom) {
                    // Find the conv UOM
                    $item = $transData->item_num;
                    $converted_uom =  $transData->uom;
                    $baseuom = $item_uom;
                    $resp = UomConv::select('conv_factor')->where('uom_from', $baseuom)->where('uom_to', $converted_uom)->where('item_num', $item)->where('site_id', auth()->user()->site_id)->pluck('conv_factor')->first();
                } else {
                    $resp = 1;
                }
                $transData->merge([
                    'qty_conv' => $transData->qty / $resp,
                ]);
                //$result = SapCallService::postCOUnPicking($transData);
            }
            $check++;
        }
        $updatePalletStatus = Container::where('lpn_num', $record[1]['lpn_num'] ?? $record[1][1]['lpn_num'])->update(['status' => 'Open']);
        if ($total_record == $check) {
            return 'true';
        } else {
            return "false";
        }
    }
}
