<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\User;
use App\Item;
use App\CustomerReturn;
use App\CustomerReturnLine;
use App\Warehouse;
use App\Loc;
use Illuminate\Support\Facades\Gate;

class CustomerReturnCatchWeightTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $item;
    protected $customerReturn;
    protected $customerReturnLine;
    protected $warehouse;
    protected $location;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'email' => '<EMAIL>'
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::factory()->create([
            'whse_num' => 'TEST_WH',
            'whse_status' => 1,
            'site_id' => 1
        ]);

        // Create test location
        $this->location = Loc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'loc_status' => 1,
            'site_id' => 1
        ]);

        // Create catch weight enabled item
        $this->item = Item::factory()->create([
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'item_status' => 1,
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create regular item
        Item::factory()->create([
            'item_num' => 'REG_ITEM_001',
            'item_desc' => 'Regular Test Item',
            'item_status' => 1,
            'catch_weight' => 0,
            'catch_weight_tolerance' => 0,
            'lot_tracked' => 0,
            'site_id' => 1
        ]);

        // Create test customer return
        $this->customerReturn = CustomerReturn::factory()->create([
            'return_num' => 'CR001',
            'cust_num' => 'CUST001',
            'status' => 'O',
            'site_id' => 1
        ]);

        // Create test customer return line
        $this->customerReturnLine = CustomerReturnLine::factory()->create([
            'return_num' => 'CR001',
            'return_line' => '1',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'qty_required' => 100.0,
            'qty_returned' => 0.0,
            'uom' => 'KG',
            'status' => 'O',
            'site_id' => 1
        ]);
    }

    /** @test */
    public function it_validates_catch_weight_route_exists()
    {
        $this->assertTrue(
            collect(\Route::getRoutes())->contains(function ($route) {
                return $route->getName() === 'runCustomerReturnCWProcess';
            })
        );
    }

    /** @test */
    public function it_shows_catch_weight_view_for_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasCustomerReturnMobile')
            ->andReturn(true);

        $this->actingAs($this->user);

        $response = $this->get(route('CustomerReturnProcess', [
            'return_num' => 'CR001',
            'return_line' => '1',
            'item_num' => base64_encode('CW_ITEM_001'),
            'cust_num' => 'CUST001',
            'whse_num' => 'TEST_WH'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('Receiving.custreturn.process_cw');
        $response->assertViewHas('Cust_Return');
        $response->assertViewHas('batch_id');
        $response->assertViewHas('printLabel');
        $response->assertViewHas('allow_over_return');
    }

    /** @test */
    public function it_shows_regular_view_for_non_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasCustomerReturnMobile')
            ->andReturn(true);

        // Create regular customer return line
        CustomerReturnLine::factory()->create([
            'return_num' => 'CR002',
            'return_line' => '1',
            'whse_num' => 'TEST_WH',
            'item_num' => 'REG_ITEM_001',
            'item_desc' => 'Regular Test Item',
            'qty_required' => 100.0,
            'qty_returned' => 0.0,
            'uom' => 'EA',
            'status' => 'O',
            'site_id' => 1
        ]);

        $this->actingAs($this->user);

        $response = $this->get(route('CustomerReturnProcess', [
            'return_num' => 'CR002',
            'return_line' => '1',
            'item_num' => base64_encode('REG_ITEM_001'),
            'cust_num' => 'CUST001',
            'whse_num' => 'TEST_WH'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('Receiving.custreturn.process');
        $response->assertViewHas('Cust_Return');
        $response->assertViewHas('batch_id');
    }

    /** @test */
    public function it_redirects_catch_weight_items_from_regular_process()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('ReturnCustomer'), [
            'return_num' => 'CR001',
            'return_line' => '1',
            'item_num' => 'CW_ITEM_001',
            'cust_num' => 'CUST001',
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'qty' => 10,
            'batch_id' => 'TEST_BATCH_001'
        ]);

        $response->assertRedirect();
        $response->assertRedirect(route('CustomerReturnProcess', [
            'return_num' => 'CR001',
            'return_line' => '1',
            'item_num' => 'CW_ITEM_001',
            'cust_num' => 'CUST001',
            'whse_num' => 'TEST_WH'
        ]));
    }
}
