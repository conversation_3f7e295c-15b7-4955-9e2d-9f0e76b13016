<?php

return [
    'title' => [
        'home' => 'Home',
        // Inquiry
        'inquiry' => 'Inquiry',
        'item_inquiry' => 'Item Inquiry',
        'loc_inquiry' => 'Location Inquiry',
        'job_inquiry' => 'Job Inquiry',
        'job_att_inquiry' => 'Job Attachment Inquiry',
        // Warehouse
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        // Incoming
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        // Outgoing
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        // WIP
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        // Print Label
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        'home' => 'Home',
        // PO Receipt
        'list_po_receipt' => 'Purchase Order Receipt List',
        'list_po' => 'PO List',
        'list_po_line' => 'PO Line List',
        // TO Receipt
        'list_to_receipt' => 'Transfer Order Receipt List',
        // TO Loss
        'to_loss' => 'TO Loss',
        'to_loss_items' => 'TO Loss Items',
        'to_loss_process' => 'TO Loss Process',
        'to_loss_catch_weight' => 'TO Loss - Catch Weight',
        // CO Picking
        'list_co_pick' => 'Customer Order Picking List',
        // CO Unpicking
        'list_co_unpick' => 'Customer Order Unpicking List',
        // Job Material Issue
        'list_job_matl_issue' => 'Job Material Issue List',
        // Job Material Return
        'list_job_matl_return' => 'Job Material Return List',
        // CO Shipping
        'list_co_ship' => 'Customer Order Shipping List',
        // Inventory Count
        'list_inv_count' => 'Inventory Count List',
        // Job Inquiry
        'list_job_inquiry' => 'Job Inquiry List',
        // Job Attachment Inquiry
        'list_job_attachment_inquiry' => 'Job Attachment Inquiry List',
        // Batch Job Material Issue
        'list_batch_job_matl_issue' => 'Batch Job Material Issue List',
        // Title for Reason Code
        'list_reason_code_title' => 'The reason code for this transaction is flagged as \'No Sync to SAP\'.\n  Please re-enter the item number to confirm.',
        // Title for Weight Detail form
        'weight_detail' => 'Weight Detail',

    ],

    'label' => [
        // A
        'all' => 'All',
        'actual_weight' => 'Actual Weight',
        // B
        'batch' => 'Batch',
        'batch_name' => 'Batch Name',
        'bundle_lot' => 'Bundle Lot',
        'box' => 'Box',

        // C
        'close_job' => 'Close Job',
        'complete_operation' => 'Complete Operation',
        'content' => 'Content',
        'co_line' => 'Line',
        'co_num' => 'CO',
        'co_rel' => 'Release',
        'counted' => 'Counted',
        'count_qty' => 'Counted Qty',
        'count_sheet' => 'Count Sheet',
        'count_sheet_id' => 'Count Sheet ID',
        'cust_name' => 'Customer Name',
        'cust_num' => 'Customer',
        'cust_order' => 'Customer Order',
        'contamination_count' => 'Contamination Count',
        'select_by' => 'Select By',
        'search_by' => 'Search By',
        'conv_qty'  => 'Converted Qty',
        'conv_uom' => 'Converted UOM',
        'count' => 'Count',
        // D
        'datetime' => 'Date Time',
        'date' => 'Date',
        'details' => 'Details',
        'doc' => 'Doc',
        'defult_issue_loc' => "Issue Location",
        'document' => 'Document',
        'due_date' => 'Due Date',
        'desc' => 'Description',
        // E
        'expiry_date' => 'Expiry Date',
        'mfg_date' => 'Manufacturing Date',
        'emp_num' => 'Emp. ID',
        'end_date' => 'End Date',
        'end_time' => 'End Time',
        // F
        'from_loc' => 'From Loc',
        'from_whse' => 'From Whse',
        'from_lot' => 'Lot',
        // G
        'grn' => 'GRN',
        'grn_line' => 'Line',
        'grn_num' => 'GRN',
        'gross_weight' => 'Gross Weight',
        'gsm'          => 'GSM (g/m2)',
        // H
        'height' => 'Height',
        'home_menu' => 'Home',
        // I
        'indirect_task' => 'Indirect Task',
        'item_desc' => 'Item Description',
        'item_num' => 'Item',
        'itemnvendor' => 'Item & Vendor',
        // J
        'job' => 'Job',
        'job_date' => 'Job Date',
        'job_matl' => 'Job Material',
        'job_num' => 'Job Order',
        'job_qty' => 'Job Quantity',
        'job_quantity' => 'Job Qty',
        'job_route' => 'Job Route',
        'job_type' => 'Job Type',
        'joint' => 'Has Joint',
        'jr_lot' => 'JR Lot',
        // L
        'label' => 'Label',
        'labels_per_box' => 'Labels Per Box',
        'last_receive' => 'Last Receive?',
        'last_return' => 'Last Return?',
        'last_pick_ship' => 'Last Pick and Ship?',
        'last_transacted_oper' => 'Last Trans. Operation',
        'last_trans_oper' => 'Last Trans. Oper',
        'line_staged' => 'Line Staged',
        'lines'=>"lines",
        'last_ship' => 'Last Ship?',
        'location' => 'Location',
        'loc_num' => 'Loc',
        'packloc_num' => 'Packing Loc',
        'lot_num' => 'Lot',
        'lpn' => 'LPN',
        'letdown' => 'Letdown',
        'line' => 'Line',
        // M
        'material' => 'Material',
        'matl_item' => 'Material',
        'meta_count' => 'Metal Count',
        // N
        'no_of_box' => 'No. of Boxes',
        'note' => 'Notes',
        'no_of_item'  => 'No. of Item',
        'no_of_reels'  => 'No. of Reels',
        'no_of_pallets'  => 'No. of Pallet',
        'name' => "Name",
        'next_res_id' => 'Next Machine ID',
        'no_of_pallet' => 'No. of Pallets',
        // O
        'oper_num' => 'Operation',
        'order_due' => 'Order Due',
        'order_num' => 'Order Num',
        // P
        'picker_id' => 'Picker ID',
        'picklist' => 'Pick List',
        'pick_method' => 'Pick Method',
        'po_line' => 'Line',
        'po_num' => 'PO',
        'po_rel' => 'Release',
        'packing_loc' => 'Packing Loc',
        'packing_list' => 'Packing List',
        'package_id' => 'Package ID',
        'partial' => 'Partial',
        'pick_by' => 'Pick By',
        'pallet' => 'Pallet',
        'pallet_source' => 'Pallet Source',
        'pm_weight'     => 'PM Weight',
        'printer' => 'Printer',

        // Q
        'qty' => 'Qty',
        'qty_allocate' => 'Qty Allocate',
        'qty_available' => 'Qty Available',
        'qty_allocated_total' => 'Total Qty Allocated',
        'qty_complete' => 'Qty Complt.',
        'qty_completed' => 'Qty Complt.',
        'qty_issued' => 'Qty Issued',
        'qty_loss' => 'Qty Loss',
        'qty_move' => 'Qty to Move',
        'qty_moved' => 'Qty Moved',
        'qty_on_hand' => 'Qty on Hand',
        'qty_open' => 'Qty Bal.',
        'qty_on_hand_conv' => '',
        'qty_per_box' => 'Qty Per Box',
        'qty_picked' => 'Qty Picked',
        'qty_receive' => 'Qty Receive',
        'qty_received' => 'Qty Received',
        'qty_receivable' => 'Qty Receivable',
        'qty_released' => 'Qty Released',
        'qty_remaining' => 'Qty Remaining',
        'qty_required' => 'Qty Req.',
        'qty_required_balance' => 'Balance Qty Req.',
        'qty_returnable' => 'Qty Returnable',
        'qty_scrap' => 'Qty Scrapped',
        'qty_scrapped' => 'Qty to Scrap',
        'qty_shipped' => 'Qty Shipped',
        'qty_staged' => 'Qty Picked',
        'qty_to_complete' => 'Qty to Complete',
        // 'qty_to_complete' => 'Qty to Complt',
        'qty_to_issue' => 'Qty to Issue',
        'qty_to_move' => 'Qty to Move',
        'qty_to_pick' => 'Qty to Pick',
        'qty_to_print' => 'Qty',
        'qty_to_putaway' => 'Qty to Putaway',
        'qty_to_receive' => 'Qty to Receive',
        'qty_to_return' => 'Qty to Return',
        'qty_to_ship' => 'Qty to Ship',
        'qty_to_unpick' => 'Qty to Unpick',
        'qty_to_put' => 'Qty to Put',
        'qty_contained' => 'Qty Contained',
        'qty_transfer' => 'Qty to Transfer',
        'qty_to_letdown' => 'Qty To Letdown',
        'qty_to_loss' => 'Qty To Lost',
        'available_for_loss' => 'Available for Loss',
        'items_available_for_loss' => 'Items Available for Loss',
        'select_reason' => 'Select Reason Code',
        'select_reason_code' => 'Select Reason Code',
        'select_warehouse' => 'Select Warehouse',
        'transfer_order' => 'Transfer Order',
        'catch_weight' => 'Catch Weight',
        'tolerance' => 'Tolerance',
        'shipped' => 'Shipped',
        'received' => 'Received',
        'lost' => 'Lost',
        // R
        'reason_code' => 'Reason Code',
        'release' => 'Release',
        'remarks' => 'Remarks',
        'res_id' => 'Machine ID',
        'receipt_type' => 'Receipt Type',
        'receive_by' => 'Receive By',
        'reels' => 'Reels',
        'reel_length' => 'Reel Length (m)',
        'reel_width' => 'Reel Width (mm)',
        'reel_bundle' => 'Reel/Bundle',
        'return_type' => 'Return Type',
        'return_num' => 'Return Number',
        'return_line' => 'Return Line',
        'row' => 'Row',
        'rows' => 'Row(s)',
        // S
        'shipping_zone' => 'Shipping Zone',
        'suffix' => 'Suffix',
        'scrapped' => 'Scrapped',
        'search' => 'Search',
        'select_all' => 'Select All',
        'sequence' => 'Sequence',
        'shift' => 'Shift',
        'ship_to' => 'Ship To',
        'ship_by' => 'Ship By',
        'ship_method' => 'Ship Method',
        'shipment_id' => 'Shipment ID',
        'show' => 'Show',
        'sort_by' => 'Sort By',
        'staged' => 'Qty Picked',
        'stage_loc' => 'Stage Loc',
        'start_date' => 'Start Date',
        'start_datetime' => 'Start DateTime',
        'start_time' => 'Start Time',
        'status' => 'Status',
        'stop_date' => 'Stop Date',
        'stop_datetime' => 'Stop DateTime',
        'stop_time' => 'Stop Time',
        'sap_base_entry' => 'Base Entry',
        'sap_base_line' => 'Base Line',
        'shipped_date' => 'Shipped Date',
        'single_item' => 'Single Item',
        'standard_actual' => 'Standard/Actual',
        'std_weight'      => 'Standard Weight',
        'scrap_reels'     => 'No. of Scrap Reels',
        'sales_person'    => 'Salesperson',
        // T
        'task' => 'Task',
        'task_type' => 'Task',
        'time' => 'Time',
        'title' => 'Title',
        'to' => 'to',
        'to_line' => 'Line',
        'to_loc' => 'To Loc',
        'to_num' => 'TO',
        'to_whse' => 'To Whse',
        'to_whse1' => 'To',
        'total' => 'Total',
        'trans_type' => 'Trans. Type',
        'trn_line' => 'Line',
        'trn_num' => 'TO',
        'type' => 'Type',
        'to_lpn' => 'To LPN',
        'total_weight' => 'Total Weight',
        // U
        'uncounted' => 'Uncounted',
        'uom' => 'UOM',
        'unit' => 'Unit',
        'unpick_by' => 'Unpick By',
        'uom_noexists' => 'Invalid UOM',
        // V
        'vend_do' => 'Vendor DO',
        'vend_lot' => 'Vendor Lot',
        'vend_name' => 'Vendor',
        'vend_num' => 'Vend. Code',
        'view_completed_job' => 'View Completed Job',
        // W
        'wc_num' => 'Work Center',
        'whse_num' => 'Whse',
        'warehouse' => 'Warehouse',
        'width' => 'Width',
        'weight' => 'Weight',
        // Z
        'zero_qoh' => 'Display Zero Qty On Hand',
        'zone' => 'Zone',

    ],

    'list' => [
        'co_lines' => 'CO Lines',
        'co_rels' => 'CO Releases',
        'cos' => 'Customer Orders',
        'customers' => 'Customers',
        'defect_reasons' => 'Scrap Reasons',
        'employees' => 'Employees',
        'from_locations' => 'Locations',
        'grn_num' => 'Goods Receiving Notes',
        'item_locations' => 'Item Locations',
        'items' => 'Items',
        'job_routes' => 'Operations',
        'jobs' => 'Job Orders',
        'label' => 'Label',
        'label_per_box' => 'Labels per Box',
        'locations' => 'Locations',
        'lot_locations' => 'Lot Locations',
        'lots' => 'Lots',
        'machines' => 'Machines',
        'material_items' => 'Material Items',
        'matl_items' => 'Material Items',
        'no_of_boxes' => 'No. of Boxes',
        'operations' => 'Operations',
        'pkgtype' => 'Package Types',
        'pickers' => 'Pickers',
        'po_lines' => 'PO Lines',
        'po_rels' => 'PO Releases',
        'pos' => 'Purchase Orders',
        'product_codes' => 'Product Codes',
        'reasons' => 'Reasons',
        'returnable_pos' => 'Returnable Purchase Orders',
        'return_num' => 'Return Number',
        'sequences' => 'Sequences',
        'stage_locations' => 'Stage Locations',
        'stage_loc' => 'Stage Locs',
        'tasks' => 'Indirect Tasks',
        'to_lines' => 'Transfer Lines',
        'to_locations' => 'Locations',
        'tos' => 'Transfer Orders',
        'uom' => 'UOM',
        'uoms' => 'UOMs',
        'vend_do' => 'Vendor DOs',
        'vend_lots' => 'Vend Lots',
        'warehouses' => 'Warehouses',
        'work_centers' => 'Work Centers',
        'vend_code' => 'Vendor Code',
        'lpn' => 'LPN',
        'item_num' => 'Items',
        'next_res_id' => 'Next Machine ID',

    ],

    'placeholder' => [
        // C
        'co_line' => 'CO Line',
        'co_num' => 'CO Number',
        'co_rel' => 'CO Release',
        'customer_code' => 'Customer Code',
        'customer_name' => 'Customer Name',
        'conv_qty'  => 'Converted Qty',
        'count_qty' => "Count Qty",
        'conv_uom' => 'Converted UOM',

        // D
        'doc' => 'Document',
        // E
        'emp_num' => 'Employee ID',
        // F
        'from_loc' => 'From Location Number',
        'from_whse' => 'From Whse',
        // G
        'grn_num' => 'GRN Number',
        // I
        'item_num' => 'Item',
        'item_desc' => 'Item Description',
        // J
        'job_num' => 'Job Order',
        'job_qty' => 'Job Quantity',
        'job_type' => 'Job Type',
        // L
        'line_staged' => 'Line Staged',
        'loc_num' => 'Location Number',
        'lot_num' => 'Lot Number',
        'lpn' => 'LPN',
        // M
        'material' => 'Material',
        'matl_item' => 'Material Item',
        // N
        'new_grn_num' => 'New GRN Number',
        'next_res_id' => 'Next Machine ID',
        // O
        'oper_num' => 'Operation',
        // P
        'picker_id' => 'Picker ID',
        'po_num' => 'PO Number',
        'po_line' => 'PO Line',
        'po_rel' => 'PO Release',
        // Q
        'qty' => 'Quantity',
        'qty_completed' => 'Qty Completed',
        'qty_receivable' => 'Qty Receivable',
        'qty_to_complete' => 'Qty to Complete',
        'qty_loss' => 'Qty Lost',
        'qty_completed' => 'Qty Complt.',
        'qty_complete' => 'Qty Completed',
        'qty_open' => 'Qty Required',
        'qty_move' => 'Qty Move',
        'qty_moved' => 'Qty to Move',
        'qty_on_hand' => 'Qty on Hand',
        'qty_available' => 'Qty Available',
        'qty_received' => 'Qty Received',
        'qty_required' => 'Qty Required',
        'qty_shipped' => 'Qty Shipped',
        'qty_scrapped' => 'Qty Scrapped',
        'qty_to_issue' => 'Qty to Issue',
        'qty_to_print' => 'Qty to Print',
        'qty_issued' => 'Qty Issued',
        'qty_to_pick' => 'Qty to Pick',
        'qty_to_putaway' => 'Qty to Putaway',
        'qty_to_return' => 'Qty to Return',
        'qty_to_ship' => 'Qty to Ship',
        'qty_to_receive' => 'Qty to Receive',
        'qty_to_unpick' => 'Qty to Unpick',
        'qty_released' => 'Qty Released',
        // R
        'reason_code' => 'Reason Code',
        'res_id' => 'Machine ID',
        'return_num' => 'Return Number',
        // S
        'sap_base_entry' => 'Base Entry',
        'sap_base_line' => 'Base Line',
        'search' => 'Search',
        'stage_loc' => 'Stage Location',
        'stage_loc' => 'Stage Loc',
        'suffix' => 'Suffix',
        'shift' => 'Shift',
        'scan_input' => 'Scan Axacute QR Code here...',
        'standard_actual' => 'Standard/Actual',
        // T
        'task_type' => 'Indirect Task',
        'to_loc' => 'To Location Number',
        'to_num' => 'Transfer Order Number',
        'to_line' => 'TO Line',
        'to_whse' => 'To Whse',
        'trn_num' => 'TO Number',
        'trn_line' => 'TO Line',
        // U
        'uom' => 'Unit',
        // V
        'vend_name' => 'Vendor Name',
        'vend_code' => 'Vendor Code',
        'vend_num' => 'Vendor Number',
        'vend_do' => 'Vendor DO',
        'vend_lot' => 'Vendor Lot',
        // W
        'wc_num' => 'Work Center',
        'whse_num' => 'Warehouse',

    ],

    'button' => [
        'add' => "Add",
        'allocate_item' => 'Allocate Item',
        'co_pick' => 'Process',
        'item_inquiry' => 'Details',
        'loc_inquiry' => 'Details',
        'job_inquiry' => 'Details',
        'misc_receipt' => 'Process',
        'stock_move' => 'Process',
        'misc_issue' => 'Process',
        'putaway' => 'Process',
        'last_trans' => 'Last Trans.',
        'lot' => 'Lot',
        'material' => 'Material',
        'picknship' => 'Next',
        'picknship_process' => 'Process',
        'po_receipt' => 'Next',
        'to_receipt_process' => 'Process',
        'job_matl_issue' => 'Next',
        'job_receipt' => 'Process',
        'process' => 'Process',
        'process_loss' => 'Process Loss',
        'start' => 'Start',
        'scan' => 'Scan',
        'next' => 'Next',
        'back' => 'Back',
        'stop' => 'Stop',
        'cancel' => 'Cancel',
        'print' => 'Print',
        'print_invlabel' => 'Process',
        'print_colabel' => 'Process',
        'print_polabel' => 'Process',
        'print_jobissuelabel' => 'Process',
        'print_jobreceiptlabel' => 'Process',
        'print_to_shipping_label' => 'Process',
        'pick' => 'Pick',
        'unpick' => 'Unpick',
        'route' => 'Route',
        'reset' => 'Reset',
        'restart' => 'Restart',
        'create' => 'Create',
        'view_details'     => 'Details',
        'count' => "Count",
        'update' => "Update",
        'delete' => "Delete",
    ],

    'nav' => [
        'switch_to_web' => 'Switch to Web',
        'logout' => 'Logout',
        'inquiry' => 'Inquiry',
        'item_inquiry' => 'Item Inquiry',
        'loc_inquiry' => 'Location Inquiry',
        'job_inquiry' => 'Job Inquiry',
        'job_att_inquiry' => 'Job Attachment Inquiry',
        'misc_receipt' => 'Misc Receipt',
        'stock_move' => 'Stock Move',
        'misc_issue' => 'Misc Issue',
        'to_loss' => 'TO Loss',
        'put_away' => 'Put Away',
        'picknship' => 'Pick and Ship',
        'co_picking' => 'CO Picking',
        'po_receipt' => 'PO Receipt',
        'to_receipt' => 'TO Receipt',
        'job_matl_issue' => 'Job Material Issue',
        'job_matl_return' => 'Job Material Return',
        'job_receipt' => 'Job Receipt',
        'job_return' => 'Job Return',
        'co_unpick' => 'CO UnPicking',
        'co_shipping' => 'CO Shipping',
        'inv_count' => 'Inventory Count',
        'incoming' => 'Incoming',
        'co_return' => 'CO Return',
        'customer_return' => 'Customer Return',
        'po_return' => 'PO Return',
        'outgoing' => 'Outgoing',
        'to_shipping' => 'TO Shipping',
        'start_labor' => 'Start Labor Run',
        'end_labor' => 'End Labor Run',
        'end_machine' => 'End Machine Run',
        'labor_reporting' => 'Labor Reporting',
        'machine_run' => 'Machine Run',
        'start_machine_run' => 'Start Machine Run',
        'end_machine_run' => 'End Machine Run',
        'start_machine_downtime' => 'Start Down Time',
        'end_machine_downtime' => 'End Down Time',

        'picklist' => 'Pick List',
        'warehouse' => 'Warehouse',
        'wip_move' => 'WIP Move',
        'print_label' => 'Print Label',
        'inv_label' => 'Inventory Label',
        'co_label' => 'CO Shipping Label',
        'po_label' => 'PO Receipt Label',
        'job_matl_label' => 'Job Material Issue Label',
        'job_receipt_label' => 'Job Receipt Label',
        'to_shipping_label' => 'TO Shipping Label',
        'pallet' => 'Pallet',
        'pallet_label' => 'Pallet Label',
        'pallet_move' => 'Pallet Move',
        'pallet_letdown' => 'Pallet Letdown',
        'pallet_transfer' => 'Pallet Transfer',
        'pallet_builder' => 'Pallet Builder',
        'pallet_destruction' => 'Pallet Destruction',
        'pallet_inquiry' => 'Pallet Inquiry',
        'bundle_builder' => 'Bundle Builder',
    ],

    'option' => [
        // A
        'all' => 'All',
        'all_def_val' => 'All',
        // C
        'closed' => 'Closed',
        'completed' => 'Completed',
        'counted' => 'Counted',
        'customer' => 'Customer',
        'customer_order' => 'Customer Order',
        // D
        'due_date' => 'Due Date',
        // E
        'exception' => 'Exception',
        // G
        'grn' => 'GRN',
        // I
        'indirect_type' => 'Indirect Type',
        'item_num' => 'Item',
        // J
        'job_date' => 'Job Date',
        'job_run' => 'Job Run',
        'job_setup' => 'Job Setup',

        // L
        'label_reel_type_1' => "Jumbo Rolls/Bundle",
        'label_reel_type_2' => "Reels Pre-print",
        'label_reel_type_3' => "Additional Process",

        'location_numbers' => 'Location Numbers',
        //M
        'multiple_item' => 'Multiple Item',
        // N
        'no' => 'No',
        // O
        'open' => 'Open',
        'order_line' => 'Order Line',
        'order_number_and_line' => 'Order Number & Line',
        'order_type' => 'Order Type',
        'order_num' => 'Order Number',
        // P
        'pick_num' => 'Pick List Number',
        'pl_count' => 'PL Count',
        'po' => 'PO',
        // Q
        'qty_required' => 'Qty Required',
        // R
        'released' => 'Released',
        // S
        'select_labor_type' => 'Select Labor Type',
        'ship_date' => 'Ship Date',
        'start_down_time' => 'Start Down Time',
        'start_machine_run' => 'Start Machine Run',
        'status' => 'Status',
        'stop_machine_run' => 'Stop Machine Run',
        'stop_down_time' => 'Stop Down Time',
        'single_item' => 'Single Item',

        // T
        'type' => 'Type',
        // U
        'uncounted' => 'UnCounted',
        // Y
        'yes' => 'Yes',
        // Z
        'zone' => 'Zone',
    ],

    'message' =>
    [
        'new_item_location' => 'This is a new item location',
        'new_location' => 'This is a new location',
        'new_lot_location' => 'A new lot number will be created',
        'new_lot_location2' => 'This is a new lot location ',
        'existing_lot_location' => 'Existing lot exist. Qty will be added',
        'existing_expiry_date' => 'Expiry date existed for this lot',
        'move_to_stock' => 'This is the last operation. Receive the item into stock is not allowed.',
        'doc' => 'Allowed file types : PDF, JPEG, PNG, JPG.  Max file size: 2MB',
        'doc_image' => 'Allowed file types : JPEG, PNG, JPG.  Max file size: 2MB',
        'notfound' => 'Record not found',
        'generate' => 'Generate Lot Number Definition',
        'recommended_new_lot' => 'Recommended New Lot',
        'item_expired_prompt' => 'Item :resource is expired on :resource2. Do you want to proceed?',
        'lot_expired_prompt' => 'Lot :resource is expired on :resource2. Do you want to proceed?',
        'qty_available_to_allocate' => 'Qty available to allocate',
        'batch_job_material_propmpt' => "This action will issue all the selected items according to the issuance location. Are you sure you want to continue this action?",
        'have_an_account'           => "Already have an account?",
        'login'                     => "Log in",
        'notallow_new_item_location' => "Loc does not exist.",
        'notallow_item_location_inactive' => "Loc is inactive.",
        'notallow_new_whse' => "Whse does not exist.",
        'notallow_inactive_whse' => "Warehouse :resource is inactive",
        'notallow_inactive_whse2' => "Warehouse is inactive.",
        'lpn_not_open' => "Pallet status is not Open.",
        'picked_lpn_cannot_destroy' => "Picked LPNs cannot be destroyed.",
        'transferred_lpn_cannot_destroy' => "Transferred LPNs cannot be destroyed.",
        'to_lpn_same_error' => "Can not transfer to the same LPN.",
        'lpn_restricted_single_item' => "To LPN is restricted to single item only. Move a different item in this Pallet is not allowed.",
        'sap_sucess_connection' => " Connection to SAP is established.",
        'create_new_location' => 'Create a new item location ?',
        'create_new_lot_location' => 'Create a new item lot location ?',
    ]


];
