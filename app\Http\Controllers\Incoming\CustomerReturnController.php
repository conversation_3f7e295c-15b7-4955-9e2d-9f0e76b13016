<?php

namespace App\Http\Controllers\Incoming;

use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\Services\SapCallService;
use Illuminate\Contracts\Encryption\DecryptException;
use App\Services\PickShipService;
use App\View\CoPickView;
use Illuminate\Http\Request;
use App\CustomerOrderItem;

use App\CustomerReturn;
use App\CustomerReturnLine;

use App\Services\CoPickService;
use App\Item;
use App\AlternateBarcode;
use App\Loc;
use App\ReasonCode;
use DB;
use Alert;
use App\View\TparmView;
use App\Http\Controllers\Controller;
use App\IssuedLot;
use App\Services\LotService;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use App\ItemLoc;
use App\LotLoc;
use App\UomConv;
use App\Shipment;
use Carbon\Carbon;
use App\Services\SiteConnectionService;
use App\Http\Controllers\ValidationController;
use App\Http\Controllers\BarcodeController;
use App\Services\CatchWeightService;
use App\SiteSetting;

class CustomerReturnController extends Controller {

    use \App\Traits\HasDefaultLoc;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:hasCustomerReturnMobile');
    }

    public function index($co_num = "") {

        // if (!\Gate::allows('hasCoReturn')) {
        //     return view('errors.404')->with('page', 'error');
        //     ;
        // }

        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('CustomerReturn', 'enable_warehouse');

        return view('Receiving.custreturn.index')->with('tparm', $tparm)->with('co_num', $co_num)->with('tparm', $tparm);
    }

    public function CustomerReturnDetails(Request $request) {

       //dd($request,"ssklk");

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $request = validateSansentiveValue($request);
        // $request->validate([
        //     'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        //         ], [
        //     'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        // ]);

        Session::put('request_data_custreturn', $request->all());

        // Send error if co_num's status is not open
       /* $checkCoNum = CustomerOrderItem::where('co_num', $request->co_num)->where('whse_num', $request->whse_num)->where('rel_status', '!=', "C")->exists();
        $checkWhse = CustomerOrderItem::where('co_num', $request->co_num)->where('rel_status', '!=', "C")->first();
        $checkWhseDetails = CustomerOrderItem::where('co_num', $request->co_num)->first();

        //dd($checkWhse,$request,$checkCoNum);
        if ($checkCoNum) {
            if($checkWhse->whse_num!=$request->whse_num)
            {
                //throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['co_num' => 'CO [' . $request->co_num. '] does not match Whse [' . $request->whse_num.']']);
            }
            else{
                if($checkWhseDetails->rel_status!='O')
                {
                 throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num . ' cannot be proceed due to status is completed/closed']);
                }
            }
        }
        else{
          // dd($checkWhseDetails);
            if($checkWhseDetails->rel_status!='O')
            {
                throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num . ' cannot be proceed due to status is completed/closed']);
            }
           // dd('sini',$checkWhse->rel_status);

        }*/

        $warehouse = $request->whse_num;
        $item = $request->item_num;
        $customer = $request->cust_num;
        $returnNum = $request->return_num;
        $results = DB::table('customer_returns')
        ->join('customer_return_lines', 'customer_returns.return_num', '=', 'customer_return_lines.return_num')
        ->when($warehouse, function ($query, $warehouse) {
            return $query->where('customer_return_lines.whse_num', $warehouse);
        })
        ->when($customer, function ($query, $customer) {
            return $query->where('customer_returns.cust_num', $customer);
        })
        ->when($returnNum, function ($query, $returnNum) {
            return $query->where('customer_returns.return_num', $returnNum);
        })
        ->when($item, function ($query, $item) {
            return $query->where('customer_return_lines.item_num', $item);
        })
        ->select('customer_returns.*', 'customer_return_lines.*')
        ->where('customer_return_lines.status','O') // or specific fields
        ->get();

        if (($results->count() == 1)) {
            $custReturnLines = $results->first();
            $request['return_line']= $custReturnLines->return_line;
           // dd($custReturnLines);
             $request->merge([
                'item_num' => base64_encode($custReturnLines->item_num),
                'from_list' => 0,
                'item_desc' => $custReturnLines->item_desc,
                'total_record' => 1,
                'return_num' => $custReturnLines->return_num

            ]);
            $redirec_request= $request->except('_token');
            return redirect()->route('CustomerReturnProcess', [...$redirec_request]);
        }
        //dd($results->item_desc);

        return view('Receiving.custreturn.list')->with('co_list', $results)->with('return_num', $request->return_num)->with('cust_num', $request->cust_num)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num);
    }

    public function CustomerReturnProcess(Request $request) {
       // dd($request,'ksksk');
        if (!\Gate::allows('hasCustomerReturnMobile')) {
            return view('errors.404')->with('page', 'error');
            ;
        }


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $return_num = $request->return_num;
        $return_line = $request->return_line;
        $item_num = $request->item_num;

        $cust_num = $request->cust_num;


        $Cust_ReturnHeader = new CustomerReturn();
        $Cust_ReturnHeader = $Cust_ReturnHeader->where('return_num', $return_num)->first();
        $cust_name = @$Cust_ReturnHeader->cust_name;


        $batch_id = generateBatchId("CustomerReturn");

        if ($item_num != "null") {
            $item_num = utf8_encode(base64_decode($item_num));
            $item_num = htmlspecialchars_decode($item_num);
        }
        //dd($item_num);

       $expired_track = 0;
        // Is item lot tracked?
        if($item_num!="NON-INV"){
            $itemToTrack = new Item();
            $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc', 'expiry_tracked', 'catch_weight', 'catch_weight_tolerance')
                    ->where('item_num', $item_num)
                    ->first();

                // dd($lot_tracked,$item_num);
            $lot_track = $lot_tracked->lot_tracked ?? null;
            $expired_track = @$lot_tracked->expiry_tracked;
        }

        //Get the CO Line associated with this operation
        $Cust_Return = new CustomerReturnLine();
        $Cust_Return = $Cust_Return->where('return_num', $return_num)
                ->where('return_line', $return_line)
                ->first();
       // dd($Cust_Return,$return_num,$return_line);
                // dd($co_num, $co_line, $request->co_rel);

        //Inject some stuff so the front end will look nicer :)
       // $Cust_Return['item_desc'];
       // $Cust_Return['qty_required'] = number_format($Cust_Return->qty_released - $Cust_Return->qty_shipped, 2, '.', '');

        $details = $this->getLocByRankReceipt($Cust_Return->whse_num, $Cust_Return->item_num);

        if (!$details) {
            $Cust_Return['defloc'] = '';
            $Cust_Return['defqty'] = '';
        } else {
            $Cust_Return['defloc'] = @$details->loc_num;
            $Cust_Return['defqty'] = @$details->qty_on_hand;
        }
        $tparms = new TparmView; $tparm = new TparmView;
        $def_loc = $tparms->getTparmValue('CustomerReturn', 'def_location');
        //dd(auth()->user()->whse_num);
        if ($def_loc != '') {
            $loc = json_decode($def_loc);
            $data = collect($loc);
            //dd($data,$request->whse_num);

            foreach($data as  $k ){

            if ($request->whse_num== $k->whse_num)
                $def_loc = $k->loc_num;
            else
            $def_loc = '';
            }
        } else $def_loc = $def_loc;

        // $tparm = $tparm->getTparmValue('CustomerReturn', 'enable_warehouse');
        // dd();
        $tparm = new TparmView;
        $sap_trans_order_integration= $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $totalcobatchqty = 0;
        $lot_num_cobatch = array();
        // if($sap_trans_order_integration==1)
        // {
        //     // Query Data from sap_po_batch_sync based on : Site_id , co_type = 1 , co_num , Po_line
        //     $site_id = auth()->user()->site_id;
        //     $getCOBatchDetails =  $co_details = DB::table('sap_co_batch_sync')->where('co_line', $request->co_line)->where('co_type', 2)->where('co_num', $co_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('co_line','ASC')->get()->toArray();

        //     //dd($getPOBatchDetails);
        //     $arrDataSAPBatchData = array();
        //     $count = 0;
        //     foreach($getCOBatchDetails as $key)
        //     {
        //         $Co_Item['qty_returnable'] = $Co_Item['qty_returnable'] - $key->qty_returned;
        //         $totalcobatchqty = $key->qty_returned;
        //         $lot_num_cobatch[$key->lot_num] = $key->qty_returned;

        //     }


        // }
        // dd($details);
        $tparm = new TparmView;
        $disable_create_new_item_location = $tparm->getTparmValue('CustomerReturn', 'disable_create_new_location');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');


        //dd($disable_create_new_item_location);

        if($request->from_list==2)
        {
           // dd($request);
            $frm_list = 2 ;
            $input['all'] = $request->all();
            $input['count_list'] = 1;
            $input['indicate'] = 3;
            $input['from_list'] =0;

            $url = generateRedirectUrl('CustomerReturn', $input);
            //dd(@$url->getTargetUrl());
        }
        else
        {
            $input['all'] = $request->all();
            $input['count_list'] = $request->total_record;
            $input['indicate'] = 1;
            $input['from_list'] = $request->from_list ?? 0;

            $frm_list = @$request->from_list ?? 0 ;

            $url = generateRedirectUrl('CustomerReturn', $input);

        }



       // dd($input,$url->getTargetUrl());

        // Check if item is catch weight enabled
        if($item_num != "NON-INV" && isset($lot_tracked) && $lot_tracked->catch_weight == 1) {
            // Get additional variables needed for catch weight view
            $tparm = new TparmView();
            $printLabel = $tparm->getTparmValue('CustomerReturn', 'print_label');
            $allow_over_return = $tparm->getTparmValue('CustomerReturn', 'allow_over_return');

            // Add item relationship to Cust_Return for catch weight tolerance access
            $Cust_Return->load('item');

            return view('Receiving.custreturn.process_cw')
                            ->with('Cust_Return', $Cust_Return)
                            ->with('cust_name',$cust_name)
                            ->with('lot_tracked', @$lot_track)
                            ->with('expired_tracked', @$expired_track)
                            ->with('disable_create_new_item_location', $disable_create_new_item_location)
                            ->with('unit_quantity_format', $unit_quantity_format)
                            ->with('totalcobatchqty', @$totalcobatchqty)
                            ->with('lot_num_cobatch', @$lot_num_cobatch)
                            ->with('sap_intergation', @$sap_trans_order_integration)
                            ->with('frm_list',@$frm_list)
                            ->with('cust_num', $cust_num)
                            ->with('def_loc',$def_loc)
                            ->with('url', @$url->getTargetUrl())
                            ->with('batch_id', @$batch_id)
                            ->with('printLabel', $printLabel)
                            ->with('allow_over_return', $allow_over_return);
        }

        return view('Receiving.custreturn.process')
                        ->with('Cust_Return', $Cust_Return)
                        ->with('cust_name',$cust_name)
                        ->with('lot_tracked', @$lot_track)
                        ->with('expired_tracked', @$expired_track)
                        ->with('disable_create_new_item_location', $disable_create_new_item_location)
                        ->with('unit_quantity_format', $unit_quantity_format)
                        ->with('totalcobatchqty', @$totalcobatchqty)
                        ->with('lot_num_cobatch', @$lot_num_cobatch)
                        ->with('sap_intergation', @$sap_trans_order_integration)
                        ->with('frm_list',@$frm_list)
                        ->with('cust_num', $cust_num)
                        ->with('def_loc',$def_loc)
                        ->with('url', @$url->getTargetUrl())
                        ->with('batch_id', @$batch_id);
    }

    public function returnCOValidate(Request $request)
    {
        $errors = [];

        if ($request['loc_num']) {
            $result = ValidationController::checkTransitPickingLocValidtion($request, 'loc_num', true);
            $tparm = new TparmView;
            $tparm = new TparmView;
            $disable_create_new_item_location = $tparm->getTparmValue('CustomerReturn', 'disable_create_new_item_location');

            if($disable_create_new_item_location==1)
            {
                    if ($result !== true) {
                        $errors['loc_num'] = $result;
                    }
            }

        }

        if ($request['lot_num']) {
            $result = ValidationController::checkLotNumValidtion($request, false);
            $tparm = new TparmView;

            // if ($result !== true) {
            //     $errors['lot_num'] = $result;
            // }
        }

        if ($request['batch_id'] && checkBatchIdExists($request['batch_id'])) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);

        }

        return $errors;
    }

    public function ReturnCustomer(Request $request) {

        //dd($request);

        // Check if item is catch weight enabled and redirect to catch weight processing
        $item_num = $request->item_num;
        if ($item_num && $item_num != "NON-INV") {
            $item = Item::where('item_num', $item_num)->first();
            if ($item && $item->catch_weight == 1) {
                // Redirect to catch weight processing with proper parameters
                $queryParams = [
                    'return_num' => $request->return_num,
                    'return_line' => $request->return_line,
                    'item_num' => $request->item_num,
                    'cust_num' => $request->cust_num,
                    'whse_num' => $request->whse_num
                ];

                return redirect()->route('CustomerReturnProcess', $queryParams);
            }
        }

        $tparm = new TparmView;
        $print_label = $tparm->getTparmValue('CustomerReturn', 'print_label');


        $crnum  = $request->return_num;
        $crline = $request->return_line;
        $qty    = $request->qty;
        $uom    = $request->uom;
        $whse   = $request->whse_num;
        $loc    = $request->loc_num;
        $cust_num = $request->cust_num;

        $lot    = $request->lot_num;
        $expiry_date = $request->expiry_date;
        $uniquekey = $request->batch_id;
        $transType  = "CustomerReturn";
        $transDate  = getDateTimeConverted();



        if($request->lot_num=="")
        {
            $request->merge([
                'lot_num'=>null
            ]);
        }

        // $request = validateSansentiveValue($request);

       // dd($disable_create_new_item_location);

        $validateErrors = self::returnCOValidate($request);

        if (count($validateErrors) > 0) {
            return back()->withErrors($validateErrors)->withInput();
        }

        $CRLines = new CustomerReturnLine();
        $CRLines = $CRLines->where('return_num', $request->return_num)
                ->where('return_line', $request->return_line)
                ->first();
        // Verifying COItem exist
        if(!$CRLines){

            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '['.$request->return_num.'-'.$request->return_line.']'])]);
        }
        // Verify status
        if ($CRLines->status != 'O')
        {
            throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => $CRLines->return_num])]);
        }

        // Rewrite the ReadOnly fields
        $request['return_num'] = $CRLines->return_num;
        $request['return_line'] = $CRLines->return_line;
        $request['whse_num'] = $CRLines->whse_num;
        $request['item_num'] = $CRLines->item_num;
        $arrJsonEncodeParameters = json_encode($request->except('_token'));
        $type_mode =config('icapt.transtype.customer_return');
       // $getQtyConv1 = UomConv::ConvertUomToBaseToLine($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, $cust_num, '', $type_mode);
       $selectuom = $request->uom;
       $cust_num =  $request->cust_num;
       $lineuom = CustomerReturnLine::where('return_num', $request->return_num)->where('return_line', $request->return_line)->value('uom');
       // For NON-INV
       if($request['item_num']=="NON-INV"){
            $baseuom = $lineuom;
       }
       else{
            if($request->lot_num != ""){
                $baseuom = LotLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('item_num', $request->item_num)->value('uom');
            }
            else{
                $baseuom = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->value('uom');
            }
        }


       $getQtyConv1 = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, $cust_num, '', $type_mode);
       $qty_line = $getQtyConv1['conv_qty_to_line']['qty'];

       $request->merge([
        'qty_conv'=>$qty_line,

        ]);
        $c_qty = 0;
        $site_id = auth()->user()->site_id;

        if($request['item_num']!="NON-INV"){
            $getCOBatchDetails =  $co_details = DB::table('sap_co_batch_sync')->where('co_line', $request->co_line)->where('co_type', 2)->where('co_num', $request->co_num)->where('doc_num',$request->document_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('co_line','ASC')->get()->toArray();
            if($getCOBatchDetails!=NULL)
            {
                foreach($getCOBatchDetails as $key)
                {
                    $c_qty = $c_qty + $key->qty_returned;
                }
            }
        }
        $final_qty_count = $c_qty + $request->qty_conv;
        // dd($final_qty_count);
        DB::beginTransaction();
        try {
            $request = validateSansentiveValue($request);
            if($request['item_num']!="NON-INV"){
                $request->validate([
                    'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                    'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
                        ], [
                    'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                    'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                ]);
            }
            else
            {
                $request->validate([
                      'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
                        ], [
                     'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                ]);

            }
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');




            if($CRLines->qty_required < $final_qty_count)
            {
                //throw ValidationException::withMessages([__('error.mobile.qty_custreturn_more_qty_available') ]);
                 if($CRLines->qty_required  > 1)
                {
                       $qyt_show = $CRLines->qty_required ;
                    }
                    else{

                         $qyt_show = "0.00000000";
                    }
                    //throw ValidationException::withMessages([__('error.mobile.qty_move_more_qty_available') ]);
                     //throw ValidationException::withMessages([__('error.mobile.qty_move_more_qty_available')."[ ".$qyt_show." ]"]);
                     $errors = __('error.mobile.qty_custreturn_more_qty_available')."[ ".$qyt_show." ]";

                     return back()->withErrors($errors)->withInput();
            }












           /* if ( $sap_trans_order_integration==1 && $request['item_num']!="NON-INV") {
                // Minus qty from table: issued_lots
                if($request->last_return=="Yes"){
                // Minus qty from table: issued_lots
                    if ($request->lot_num) {
                        $issued_lot = IssuedLot::where('from_module', 'Customer Return')
                                ->where('whse_num', $request->whse_num)
                                ->where('ref_num', $request->co_num)
                                ->where('ref_line', $request->co_line)
                                ->where('ref_release', $request->co_rel)
                                ->where('lot_num', $request->lot_num)
                                ->first();

                        if ($issued_lot) {
                            $issued_lot->qty = $issued_lot->qty - $final_qty_count;

                            // If less or equal to 0, delete it
                            if ($issued_lot->qty <= 0) {
                                $issued_lot->delete();
                            } else {
                                $issued_lot->save();
                            }
                        }
                    }
                }
            }
        else{
           if($request['item_num']!="NON-INV")
               {
                    if ($request->lot_num) {
                        $issued_lot = IssuedLot::where('from_module', 'Customer Return')
                                ->where('whse_num', $request->whse_num)
                                ->where('ref_num', $request->co_num)
                                ->where('ref_line', $request->co_line)
                                ->where('ref_release', $request->co_rel)
                                ->where('lot_num', $request->lot_num)
                                ->first();

                        if ($issued_lot) {
                            $issued_lot->qty = $issued_lot->qty - $request->qty_conv;

                            // If less or equal to 0, delete it
                            if ($issued_lot->qty <= 0) {
                                $issued_lot->delete();
                            } else {
                                $issued_lot->save();
                            }
                        }
                    }
                }

        }*/


        if($request['item_num']!="NON-INV")
        {
           // Store in Location
            $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
            // If not exist, store it
            if (!$checkLoc) {
                $loc = new Loc;
                $loc->whse_num = $request->whse_num;
                $loc->loc_num = $request->loc_num;
                $loc->loc_type = "S";
                $loc->loc_status = 1;
                $loc->save();
            } else {
                if ($checkLoc->loc_status == 0) {
                    throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
                }
            }
            $itemToTrack = new Item();
            // Is item lot tracked?
            $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc')
                    ->where('item_num', $request->item_num)
                    ->first();
            $lot_track = $lot_tracked->lot_tracked;
        }


            $CR_Line = new CustomerReturnLine();
            $CR_Line = $CR_Line->where('return_num', $request->return_num)
                    ->where('return_line', $request->return_line)
                    ->first();
            $CR_Line['item_desc'] = @$lot_tracked->item_desc ?? $CR_Line->item_desc;
            Session::put('modulename', 'CustomerReturn');
            Session::put('return_num', $request->return_num);
            Session::put('whse_num', $CR_Line->whse_num);
            Session::put('cust_num',  $request->cust_num);

           // Once SAP Integration On not In
           // if ($sap_trans_order_integration != 1) {
                $coReturnRequest = PickShipService::executeCustomerReturn($request);

                if($request['item_num']!="NON-INV"){
                     LotService::updateLot("Customer Return", $request);
                }

           // }
           // dd($request);
            //$coitem = new CustomerOrderItem();
            //$checkActive = $coitem->checkActiveCo($request->co_num);

            // SAP Intergration
//                         dd($sap_trans_order_integration, $request->last_return);

            //$syncresult = ReasonCode::where('reason_num', $request->reason_code)->where('reason_class', 'CustomerReturn')->first();

            // Store CO Return Information into Shipment (issue #1988)
            // $shipment = Shipment::create([
            //     'shipment_id' => Shipment::orderBy('shipment_id','desc')->value('shipment_id') + 1,
            //     'order_detail' => json_encode($Co_Item),
            //     'qty_shipped' => $getQtyConv1['conv_qty_to_line']['qty'], // positive value because stock level increase (#1988)
            //     'uom' => $getQtyConv1['conv_qty_to_line']['uom'],
            //     'co_num' => $Co_Item->co_num,
            //     'co_line' => $Co_Item->co_line,
            //     'lot_num' => @$request->lot_num,
            //     'trans_type' => "3",
            //     'shipment_date' => Carbon::now() // UTC Timezone
            // ]);

            /*if ($sap_trans_order_integration == 1 && $syncresult && $request['item_num']!="NON-INV") {
                 $base_qty_conv = $getQtyConv1['conv_qty_to_base']['qty'];
                 $request['qty_conv'] = $base_qty_conv ;

                DB::table('sap_co_batch_sync')->insert([
                    'co_num' => $request->co_num,
                    'co_line' => $request->co_line,
                    'co_rel' => $co_rel ?? 0,
                    'erp_ID' => CustomerOrderItem::select('erp_ID')->where('co_num', $request->co_num)
                            ->where('co_line', $request->co_line)
                            //->where('co_rel', $co_rel)
                            ->where('site_id', auth()->user()->site_id)
                            ->value('erp_ID'),
                    'whse_num' => $request->whse_num,
                    'co_type' => 2,
                    'loc_num' => $request->loc_num,
                    'lot_num' => $request->lot_num ?? null,
                    'item_num' => $request->item_num ,
                    'cust_num' => $request->cust_num,
                    'uom' => $request->uom,
                    'qty_returned' => $request->qty,
                    'qty_conv' =>  $request->qty_conv,
                    'doc_num' => $request->document_num,
                    'sync_status' => 1,
                    'sap_base_entry' => $request->sap_base_entry,
                    'sap_base_line' => $request->sap_base_line,
                    'is_return' => 1,
                    'site_id' => auth()->user()->site_id,
                    'created_by' => auth()->user()->name,
                    'modified_by' => auth()->user()->name,
                    'created_date' => now(),
                    'modified_date' => now(),
                    'json_parameters' => $arrJsonEncodeParameters
                ]);


                if ($request->last_return == "Yes") {

                    $co_details = DB::table('sap_co_batch_sync')->where('CO_type', 2)->where('cust_num', $request->cust_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->get()->toArray();

                    if(isset($co_details))
                    {
                        foreach($co_details as $key => $value){
                            $arrJsonDeEncodeparameters = json_decode($value->json_parameters,true);

                           $request->merge([]);
                           //$arr = $request->merge([$arrJsonDeEncodeparameters]);
                           $request = $request->merge($arrJsonDeEncodeparameters);

                           $coReturnRequest = PickShipService::executeCoReturn($request);
                           LotService::updateLot("CO Return", $request);

                        }

                    }
                }
                 else
                 {
                    if($syncresult->sync_status == "N"){
                        $co_details = DB::table('sap_co_batch_sync')->where('CO_type', 2)->where('cust_num', $request->cust_num)->where('doc_num', $request->document_num)->where('site_id', auth()->user()->site_id)->whereIn('sync_status', [1, 2])->get()->toArray();

                        if(isset($co_details))
                        {
                            foreach($co_details as $key => $value){
                                $arrJsonDeEncodeparameters = json_decode($value->json_parameters,true);

                               $request->merge([]);
                               //$arr = $request->merge([$arrJsonDeEncodeparameters]);
                               $request = $request->merge($arrJsonDeEncodeparameters);

                               $coReturnRequest = PickShipService::executeCoReturn($request);
                               LotService::updateLot("CO Return", $request);

                            }

                        }
                    }
                 }
                    //$res = SapCallService::postCOReturn($request, null);

                    if($syncresult->sync_status == "Y" && $request->last_return == "Yes")
                    {

                        if( $sap_single_bin ==1){

                            $result = SiteConnectionService::postIntergrationTransPO("CO Return", $request,null);

                           // $result = SapCallService::postPOReturn($request, null);
                        }
                        else{
                            if(config('icapt.enable_sap_ap_readfrom_maltrans'))
                            {
                                // Later Change to Maltrans
                                $result =  SapCallService::postCOReturn($request, null);

                            }
                            else
                            {
                                $result = SapCallService::postCOReturn($request, null);

                            }
                         }
                        if($result!=200)
                        {

                            Alert::error( __('error.mobile.sap_error'), __('error.mobile.sap_error_contact').$result)->persistent('Dismiss');
                        }
                    }

            }*/
            Alert::success('Success', __('success.processed', ['process' => __('Customer Return')]));

            DB::commit();
            //return app('App\Http\Controllers\RouteController')->BackButton();


            $printLabelReq = $request;
         if($print_label==1){
         if ($printLabelReq->lot_num != null) {
             $check_expiry_date = LotService::getExpiryDate($printLabelReq);
              // Generate barcode
             // changed $request->qty to $qty because $request->qty was empty
             $print_input = app('App\Http\Controllers\BarcodeController')->GetCustReturnLabelDataMuplti($crnum, $crline,  $qty, $uom, $whse, $loc, $lot, $expiry_date, $uniquekey, $transDate, $transType);
         } else {
              // Generate barcode
             $print_input = app('App\Http\Controllers\BarcodeController')->GetCustReturnLabelDataMuplti($crnum, $crline,  $qty, $uom, $whse, $loc, $lot, null, $uniquekey, $transDate, $transType);
         }
        }

        if ($print_label == 1) {
           // dd($print_input,'sllslkslk');
            return BarcodeController::showLabelDefinition($print_input);
        } else {
           // dd($request.'ssss');
            return app('App\Http\Controllers\RouteController')->BackButton();
        }



        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function ReturnCustomerList(Request $request) {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        //dd($request,'ssjkjkkk18717187');

        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $return_num = $request->get('return_num');
            $return_line = $request->get('return_line');
            $whse_num = $request->get('whse_num');
            $cust_num = $request->get('cust_num');
            $item_num = $request->get('item_num');

            if ($query != '') {
                $custReturnLines = new CustomerReturnLine();
                $data = $custReturnLines->custReturnItemSearchListNew($return_num,$return_line, $whse_num,$cust_num,$item_num, $query);
                //dd($data,'ssss');
            } else {
                // $data = CustomerOrderItem::where('co_num', $co_num)
                //         ->where('whse_num', $whse_num)
                //         ->where('rel_status', '!=', 'C')
                //         ->where('co_line', 'like', '%' . $request->co_line . '%')
                //         ->where('item_num', 'like', '%' . $item_num . '%')
                //         ->where('qty_returnable', '>', 0)
                //         ->orderBy('co_num')
                //         ->get();



                // $warehouse = $request->whse_num;
                // $item = $request->item_num;
                // $customer = $request->cust_num;
                // $returnNum = $request->return_num;
                $data = DB::table('customer_return_lines')
                ->join('customer_returns', 'customer_returns.return_num', '=', 'customer_return_lines.return_num')
                ->when($whse_num, function ($query, $whse_num) {
                    return $query->where('customer_return_lines.whse_num', $whse_num);
                })
                ->when($cust_num, function ($query, $cust_num) {
                    return $query->where('customer_returns.cust_num', $cust_num);
                })
                ->when($return_num, function ($query, $return_num) {
                    return $query->where('customer_return_lines.return_num', $return_num);
                })
                ->when($item_num, function ($query, $item_num) {
                    return $query->where('customer_return_lines.item_num', $item_num);
                })
                ->select('customer_returns.*', 'customer_return_lines.*')
                ->where('customer_return_lines.status','O') // or specific fields
                ->get();


               //dd($data,'5555');
            }

            $total_row = $data->count();


            $tparm = new TparmView;
            $sap_trans_order_integration= $tparm->getTparmValue('System', 'sap_trans_order_integration');

            // if($sap_trans_order_integration==1)
            // {
            //     // Query Data from sap_po_batch_sync based on : Site_id , co_type = 1 , co_num , co_line
            //     $site_id = auth()->user()->site_id;
            //     $getCOBatchDetails =  $co_details = DB::table('sap_co_batch_sync')->where('co_type', 2)->where('co_num', $co_num)->where('site_id', $site_id)->whereIn('sync_status', [1])->orderBy('co_line','ASC')->get()->toArray();

            //     //dd($getPOBatchDetails);
            //     $arrDataSAPBatchData = array();
            //     $count = 0;
            //     foreach($getCOBatchDetails as $key)
            //     {
            //         $arrDataSAPBatchData[$key->co_line] = $count + $key->qty_returned;

            //     }


            // }


            if ($total_row > 0) {
                foreach ($data as $row) {
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                   // $qty_balance = $row->qty_returnable ;
                    // if SAP ON need to check sap_po_batch_sync table
                //    if($sap_trans_order_integration==1)
                //    {
                //        if(@$arrDataSAPBatchData[$row->co_line] > 0)
                //        {
                //            $qty_balance = $row->qty_returnable - @$arrDataSAPBatchData[$row->co_line];
                //           // $qty_required = $row->qty_balance - @$arrDataSAPBatchData[$row->po_line];
                //        }
                //    }


                    $output .= '
                        <div class="container" >
                            <form class="form" autocomplete="off" id="myform" method="GET" action="/home/<USER>/customer-return/details/process">
                                <div class="row border  border-primary" id="mybox" onclick="javascript:this.parentNode.submit();" >
                                <input type="hidden" name="_token" value="' . csrf_token() . '">

                                    <div class="col-xs-12">
                                        <table style="width: 100%;">
                                         <input type="hidden" value="' . $total_row . '" name="total_record">
                                         <input type="hidden" value="1" name="from_list">
                                            <input type="hidden" value="' . $row->return_num . '" name="return_num">
                                            <input type="hidden" value="' . $row->return_line . '" name="return_line">
                                            <input type="hidden" value="' . $cust_num . '" name="cust_num">
                                             <input type="hidden" value="' . $whse_num . '" name="whse_num">
                                            <tr>

                                                <td>
                                                    <label for="return_num">' . __('mobile.label.return_num') . '</label>
                                                </td>
                                                <td>
                                                 <span class="form-control border-primary pseudoinput">' . $row->return_num . ' </span>
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->return_line . ' </span>
                                                    <input type="hidden" id="return_line" class="form-control border-primary" value="' . $row->return_line . '" readonly>

                                                     <input type="hidden" name="frm_list" value=1>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="item_num">' . __('mobile.label.item_num') . '</label>
                                                </td>
                                                <td colspan="4">';
                                                    foreach ($altBarCode as $barCode){
                                                        $output .= '<span style="display: none"> '.$barCode->alternate_barcode.' </span>';
                                                    }
                                                $output .= '
                                                    <span class="form-control border-primary pseudoinput">' . $row->item_num . '</span>
                                                    <input size="15" readonly type="hidden" style="text-align:left;" name="item_num" class="form-control border-primary" value="' . base64_encode($row->item_num). '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="4">
                                                    <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                                    <input name="item_desc" size="15" readonly type="hidden" style="text-align:left;" class="form-control border-primary" value="' . $row->item_desc . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <label for="qty_returnable">' . __('mobile.label.qty_required') . '</label>
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '</span>
                                                    <input readonly type="hidden" class="form-control border-primary" name="qty_req" style="text-align:right;" size="7" value="' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '">
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                                    <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                    ';
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }



    public function backshowCustomerReturnLineBack(Request $request)
    {
        //dd('sjsjsj',$request);
        return $this->ReturnCustomerList($request);
    }

    public function runCustomerReturnCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $request = validateSansentiveValue($request);

        // Get customer return line details
        $return_num = $request->ref_num;
        $return_line = $request->ref_line;

        $Cust_Return = CustomerReturnLine::where('return_num', $return_num)
                        ->where('return_line', $return_line)
                        ->first();

        if (!$Cust_Return) {
            throw ValidationException::withMessages(['return_num' => __('error.admin.return_not_found')]);
        }

        // Get tolerance and UOM for catch weight validation (returnable quantity)
        $tolerance = $Cust_Return->qty_required;
        $tolerance_uom = $Cust_Return->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $Cust_Return->uom,
            'qty_to_return' => array_sum($request->arr_qty ?? []),
            'return_num' => $request->ref_num,
            'return_line' => $request->ref_line,
            'item_num' => $request->item_num,
            'whse_num' => $request->whse_num,
            'uom' => $Cust_Return->uom,
            'cust_num' => $request->cust_num
        ]);

        DB::beginTransaction();
        try {
            // Use CatchWeightService to handle inventory updates
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans(
                $request,
                $tolerance_uom,
                'Customer Return',
                "Customer Return"
            );

            // Update customer return line quantities
            if ($Cust_Return) {
                $Cust_Return->qty_returned = $Cust_Return->qty_returned + $request->qty_to_return;
                $Cust_Return->save();
            }

            DB::commit();

            Alert::success('Success', __('success.processed', ['process' => __('Customer Return')]));

            return redirect()->route('CustomerReturn');

        } catch (Exception $e) {
            DB::rollback();
            throw ValidationException::withMessages(['error' => $e->getMessage()]);
        }
    }



}
