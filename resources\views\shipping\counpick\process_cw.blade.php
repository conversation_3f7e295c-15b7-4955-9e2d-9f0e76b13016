@extends('layout.mobile.app')
@section('content')
@section('title', __('Customer Order UnPicking - Catch Weight'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$stageLoc->whse_num"
            :itemnum="$stageLoc->item_num"
            :itemdesc="$stageLoc->item_desc"
            :refnum="$stageLoc->co_num"
            :refline="$stageLoc->co_line"
            :qtybalance="$stageLoc->qty_staged ?? 0"
            :qtybalanceuom="$stageLoc->uom"
            :submiturl="route('runCoUnPickCWProcess')"
            :catch-weight-tolerance="$stageLoc->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="0"
            :allow-over="$allow_over_unpick"
            :line-uom="$stageLoc->uom"
            :print-label="$printLabel"
            transtype="co"
            trans-type="COUnPick"
            :incoming="true">

            {{-- CO UnPicking specific hidden fields --}}
            <input type="hidden" name="co_rel" value="{{ $stageLoc->co_rel }}">
            <input type="hidden" name="cust_num" value="{{ $cust_num }}">
            <input type="hidden" name="stage_num" value="{{ $stage_num }}">
            <input type="hidden" name="qty_staged" value="{{ $stageLoc->qty_staged }}">
            <input type="hidden" name="qty_on_hand" value="{{ $stageLoc->qty_on_hand }}">
            <input type="hidden" name="base_uom" value="{{ $stageLoc->base_uom }}">
            <input type="hidden" name="lot_tracked" value="{{ $lot_tracked }}">
            <input type="hidden" name="getCheckNONINV" value="{{ $getCheckNONINV }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.co_num') }}: {{ $stageLoc->co_num }} | {{ __('mobile.label.co_line') }}: {{ $stageLoc->co_line }}
                        </p>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.stage_loc') }}: {{ $stage_num }}
                        </p>
                    </div>
                </div>
            </x-slot>
        </x-catch-weight-form>
    </div>
</div>

@endsection
