@extends('layout.mobile.app')
@section('content')
@section('title', __('mobile.title.to_loss_catch_weight'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }
    
    .info-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .info-row {
        margin-bottom: 8px;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
    }
    
    .info-value {
        color: #007bff;
    }
    
    .catch-weight-badge {
        background-color: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-left: 10px;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <!-- Item Information Section -->
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.transfer_order') }}:</span>
                <span class="info-value">{{ $transferLine->trn_num }} - {{ $transferLine->trn_line }}</span>
                <span class="catch-weight-badge">{{ __('mobile.label.catch_weight') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.item') }}:</span>
                <span class="info-value">{{ $transferLine->item_num }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.description') }}:</span>
                <span>{{ $transferLine->item->item_desc ?? '' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.available_for_loss') }}:</span>
                <span class="info-value">{{ numberFormatPrecision($qtyAvailableForLoss, 2) }} {{ $transferLine->uom }}</span>
            </div>
            @if($item->catch_weight_tolerance)
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.tolerance') }}:</span>
                <span class="info-value">±{{ $item->catch_weight_tolerance }}%</span>
            </div>
            @endif
        </div>

        <x-catch-weight-form
            :whsenum="$whse_num"
            :itemnum="$transferLine->item_num"
            :itemdesc="$transferLine->item->item_desc ?? ''"
            :refnum="$transferLine->trn_num"
            :refline="$transferLine->trn_line"
            :qtybalance="$qtyAvailableForLoss"
            :qtybalanceuom="$transferLine->uom"
            :submiturl="route('processToLossCW')"
            :catch-weight-tolerance="$item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="1"
            :allow-over="false"
            :line-uom="$transferLine->uom"
            :print-label="false"
            transtype="toloss"
            trans-type="TOLoss"
            :incoming="false">
            
            <!-- Additional hidden fields for TO Loss -->
            <input type="hidden" name="trn_num" value="{{ $transferLine->trn_num }}" />
            <input type="hidden" name="trn_line" value="{{ $transferLine->trn_line }}" />
            <input type="hidden" name="item_num" value="{{ $transferLine->item_num }}" />
            <input type="hidden" name="whse_num" value="{{ $whse_num }}" />
            <input type="hidden" name="uom_loss" value="{{ $transferLine->uom }}" />
            <input type="hidden" name="transit_loc" value="{{ $transitLoc }}" />
            
            <!-- Reason Code Selection -->
            <div class="form-group row">
                <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                    for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <select class="form-control" id="reason_code" name="reason_code" required>
                        <option value="">{{ __('mobile.label.select_reason') }}</option>
                        @foreach ($reasonCodes as $reasonCode)
                            <option value="{{ $reasonCode->reason_num }}" 
                                {{ old('reason_code') == $reasonCode->reason_num ? 'selected' : '' }}>
                                {{ $reasonCode->reason_num }} - {{ $reasonCode->reason_desc }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-xs-2 col-md-2 col-lg-2">
                    <button type="button" id="reason_code_btn"
                        class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                        data-target="#reasonModal"><i class="icon-search"></i></button>
                </div>
            </div>

            <!-- Reference Field -->
            <div class="form-group row">
                <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                    for="reference">{{ __('mobile.label.reference') }}</label>
                <div class="col-xs-9 col-md-9 col-lg-9">
                    <input type="text" id="reference" class="form-control" placeholder="{{ __('mobile.placeholder.reference_optional') }}"
                        name="reference" value="{{ old('reference') }}">
                </div>
            </div>
        </x-catch-weight-form>
    </div>
</div>

<!-- Reason Code Selection Modal -->
<div class="modal fade text-xs-left" id="reasonModal" tabindex="-1" role="dialog" aria-labelledby="reasonModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title modalheader"><b>{{ __('mobile.title.select_reason_code') }}</b></h4>
            </div>
            <div class="modal-body">
                <div class="container">
                    @foreach ($reasonCodes as $reasonCode)
                        <div class="row" style="cursor: pointer;" onclick="selectReasonCode('{{ $reasonCode->reason_num }}', '{{ $reasonCode->reason_desc }}')">
                            <div class="col-xs-12">
                                <strong>{{ $reasonCode->reason_num }}</strong> - {{ $reasonCode->reason_desc }}
                            </div>
                        </div>
                        <hr>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function selectReasonCode(reasonNum, reasonDesc) {
        $('#reason_code').val(reasonNum);
        $('#reasonModal').modal('hide');
    }

    $(document).ready(function() {
        // Override the cancel button behavior to go back to item list
        $('.btn-warning[onclick*="window.history.go(-1)"]').attr('onclick', '').click(function() {
            window.location.href = "{{ route('TOLossItemList', ['whse_num' => $whse_num, 'trn_num' => $transferLine->trn_num]) }}";
        });

        // Add validation for reason code before form submission
        $('form').on('submit', function(e) {
            if (!$('#reason_code').val()) {
                e.preventDefault();
                alert("{{ __('mobile.message.reason_code_required') }}");
                $('#reason_code').focus();
                return false;
            }
        });

        // Auto-focus on reason code if not selected
        if (!$('#reason_code').val()) {
            $('#reason_code').focus();
        }
    });
</script>

@include('util.selection')
@endsection
