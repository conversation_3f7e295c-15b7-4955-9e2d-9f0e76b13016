@extends('layout.mobile.app')
@section('content')
@section('title', __('mobile.title.to_loss_process'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
    
    .info-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .info-row {
        margin-bottom: 8px;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
    }
    
    .info-value {
        color: #007bff;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block">
        <!-- Item Information Section -->
        <div class="info-section">
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.transfer_order') }}:</span>
                <span class="info-value">{{ $transferLine->trn_num }} - {{ $transferLine->trn_line }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.item') }}:</span>
                <span class="info-value">{{ $transferLine->item_num }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.description') }}:</span>
                <span>{{ $transferLine->item->item_desc ?? '' }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">{{ __('mobile.label.available_for_loss') }}:</span>
                <span class="info-value">{{ numberFormatPrecision($qtyAvailableForLoss, 2) }} {{ $transferLine->uom }}</span>
            </div>
        </div>

        <form class="form" autocomplete="off" id="TOLossProcessForm" name="TOLossProcessForm"
            action="{{ route('processToLoss') }}" method="post">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                <!-- Hidden Fields -->
                <input type="hidden" name="whse_num" value="{{ $whse_num }}">
                <input type="hidden" name="trn_num" value="{{ $transferLine->trn_num }}">
                <input type="hidden" name="trn_line" value="{{ $transferLine->trn_line }}">
                <input type="hidden" name="item_num" value="{{ $transferLine->item_num }}">
                <input type="hidden" name="uom_loss" value="{{ $transferLine->uom }}">

                @if($item && $item->lot_tracked == 1)
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="lot_num">{{ __('mobile.label.lot_number') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <input type="text" id="lot_num" class="form-control" placeholder="{{ __('mobile.placeholder.lot_number') }}"
                            name="lot_num" value="{{ old('lot_num') }}" required>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <button type="button" id="lot_num_btn"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#lotModal"><i class="icon-search"></i></button>
                    </div>
                </div>
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="qty_loss">{{ __('mobile.label.qty_loss') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <input type="number" id="qty_loss" class="form-control" placeholder="{{ __('mobile.placeholder.qty_loss') }}"
                            name="qty_loss" value="{{ old('qty_loss') }}" step="0.01" min="0.01" 
                            max="{{ $qtyAvailableForLoss }}" required>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <span class="form-control-static">{{ $transferLine->uom }}</span>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <select class="form-control" id="reason_code" name="reason_code" required>
                            <option value="">{{ __('mobile.label.select_reason') }}</option>
                            @foreach ($reasonCodes as $reasonCode)
                                <option value="{{ $reasonCode->reason_num }}" 
                                    {{ old('reason_code') == $reasonCode->reason_num ? 'selected' : '' }}>
                                    {{ $reasonCode->reason_num }} - {{ $reasonCode->reason_desc }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2">
                        <button type="button" id="reason_code_btn"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#reasonModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-2 label-control"
                        for="reference">{{ __('mobile.label.reference') }}</label>
                    <div class="col-xs-9 col-md-9 col-lg-9">
                        <input type="text" id="reference" class="form-control" placeholder="{{ __('mobile.placeholder.reference_optional') }}"
                            name="reference" value="{{ old('reference') }}">
                    </div>
                </div>

                <div class="form-actions center">
                    <button type="button" class="btn btn-warning mr-1" onclick="goBack()">
                        <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                    </button>
                    <button type="submit" id="process" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.process_loss') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Reason Code Selection Modal -->
<div class="modal fade text-xs-left" id="reasonModal" tabindex="-1" role="dialog" aria-labelledby="reasonModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title modalheader"><b>{{ __('mobile.title.select_reason_code') }}</b></h4>
            </div>
            <div class="modal-body">
                <div class="container">
                    @foreach ($reasonCodes as $reasonCode)
                        <div class="row" style="cursor: pointer;" onclick="selectReasonCode('{{ $reasonCode->reason_num }}', '{{ $reasonCode->reason_desc }}')">
                            <div class="col-xs-12">
                                <strong>{{ $reasonCode->reason_num }}</strong> - {{ $reasonCode->reason_desc }}
                            </div>
                        </div>
                        <hr>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function selectReasonCode(reasonNum, reasonDesc) {
        $('#reason_code').val(reasonNum);
        $('#reasonModal').modal('hide');
    }

    function goBack() {
        window.location.href = "{{ route('TOLossItemList', ['whse_num' => $whse_num, 'trn_num' => $transferLine->trn_num]) }}";
    }

    $(document).ready(function() {
        // Auto-focus on quantity field
        $('#qty_loss').focus();

        // Handle scan input
        $(document).on('change', '#scan_input', function() {
            var scannedValue = $(this).val();
            if (scannedValue) {
                @if($item && $item->lot_tracked == 1)
                    if (!$('#lot_num').val()) {
                        $('#lot_num').val(scannedValue);
                        $('#qty_loss').focus();
                    }
                @else
                    // For non-lot tracked items, try to parse as quantity
                    var qty = parseFloat(scannedValue);
                    if (!isNaN(qty) && qty > 0 && qty <= {{ $qtyAvailableForLoss }}) {
                        $('#qty_loss').val(qty);
                        $('#reason_code').focus();
                    }
                @endif
                $(this).val(''); // Clear scan input
            }
        });

        // Validate quantity on change
        $('#qty_loss').on('change', function() {
            var qty = parseFloat($(this).val());
            var maxQty = {{ $qtyAvailableForLoss }};
            
            if (qty > maxQty) {
                alert("{{ __('mobile.message.qty_exceeds_available') }}");
                $(this).val(maxQty);
            }
        });

        // Form validation
        $('#TOLossProcessForm').on('submit', function(e) {
            var qty = parseFloat($('#qty_loss').val());
            var maxQty = {{ $qtyAvailableForLoss }};
            
            if (qty <= 0) {
                e.preventDefault();
                alert("{{ __('mobile.message.qty_must_be_greater_than_zero') }}");
                return false;
            }
            
            if (qty > maxQty) {
                e.preventDefault();
                alert("{{ __('mobile.message.qty_exceeds_available') }}");
                return false;
            }
            
            @if($item && $item->lot_tracked == 1)
                if (!$('#lot_num').val()) {
                    e.preventDefault();
                    alert("{{ __('mobile.message.lot_number_required') }}");
                    return false;
                }
            @endif
            
            if (!$('#reason_code').val()) {
                e.preventDefault();
                alert("{{ __('mobile.message.reason_code_required') }}");
                return false;
            }
        });
    });
</script>

@include('util.selection')
@endsection
